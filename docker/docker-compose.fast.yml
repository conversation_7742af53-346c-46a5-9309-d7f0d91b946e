version: '3.8'

services:
  redis:
    healthcheck:
      interval: 5s
      timeout: 3s
      retries: 2
      start_period: 5s

  app:
    healthcheck:
      interval: 10s
      timeout: 3s
      retries: 2
      start_period: 10s

  celery-worker:
    healthcheck:
      interval: 10s
      timeout: 3s
      retries: 2
      start_period: 10s

  celery-beat:
    healthcheck:
      interval: 10s
      timeout: 3s
      retries: 2
      start_period: 10s

  flower:
    command: >
      sh -c "sleep 2 &&
             celery -A app.services.task_queue flower
             --port=5555
             --broker=redis://redis:6379/1
             --broker_api=redis://redis:6379/1
             --basic_auth=admin:admin"
    healthcheck:
      interval: 10s
      timeout: 3s
      retries: 2
      start_period: 10s
