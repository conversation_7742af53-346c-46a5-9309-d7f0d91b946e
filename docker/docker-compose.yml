services:
  # Main FastAPI application
  app:
    build:
      context: ..
      dockerfile: docker/Dockerfile
      target: development
    ports:
      - "8001:8000"
    environment:
      - DEBUG=True
      - ENVIRONMENT=development
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    env_file:
      - ../.env
    volumes:
      - ../app:/app/app
      - ../logs:/app/logs:rw
      - ../data:/app/data:rw
      - ../temp:/app/temp:rw
      - ../uploads:/app/uploads:rw
      - ../downloads:/app/downloads:rw
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - crawl-agent-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health/live"]
      interval: 15s
      timeout: 5s
      retries: 3
      start_period: 20s

  # Redis for caching and task queue
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - crawl-agent-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru

  # Celery worker for background tasks
  celery-worker:
    build:
      context: ..
      dockerfile: docker/Dockerfile
      target: development
    environment:
      - DEBUG=True
      - ENVIRONMENT=development
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    env_file:
      - ../.env
    volumes:
      - ../app:/app/app
      - ../logs:/app/logs:rw
      - ../data:/app/data:rw
      - ../temp:/app/temp:rw
      - ../uploads:/app/uploads:rw
      - ../downloads:/app/downloads:rw
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - crawl-agent-network
    restart: unless-stopped
    command: celery -A app.services.task_queue worker --loglevel=info --concurrency=4
    healthcheck:
      test: ["CMD", "celery", "-A", "app.services.task_queue", "inspect", "ping"]
      interval: 15s
      timeout: 5s
      retries: 3
      start_period: 20s

  # Celery beat for scheduled tasks
  celery-beat:
    build:
      context: ..
      dockerfile: docker/Dockerfile
      target: development
    environment:
      - DEBUG=True
      - ENVIRONMENT=development
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    env_file:
      - ../.env
    volumes:
      - ../app:/app/app
      - ../logs:/app/logs:rw
      - ../data:/app/data:rw
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - crawl-agent-network
    restart: unless-stopped
    command: celery -A app.services.task_queue beat --loglevel=info
    healthcheck:
      test: ["CMD", "celery", "-A", "app.services.task_queue", "inspect", "ping"]
      interval: 15s
      timeout: 5s
      retries: 3
      start_period: 20s

  # Flower for Celery monitoring
  flower:
    build:
      context: ..
      dockerfile: docker/Dockerfile
      target: development
    ports:
      - "5555:5555"
    environment:
      - DEBUG=True
      - ENVIRONMENT=development
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
      - FLOWER_UNAUTHENTICATED_API=true
    env_file:
      - ../.env
    depends_on:
      redis:
        condition: service_healthy
      celery-worker:
        condition: service_started
    networks:
      - crawl-agent-network
    restart: unless-stopped
    command: >
      sh -c "sleep 5 &&
             celery -A app.services.task_queue flower
             --port=5555
             --broker=redis://redis:6379/1
             --broker_api=redis://redis:6379/1
             --basic_auth=admin:admin"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5555"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Nginx reverse proxy (optional for development)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      app:
        condition: service_healthy
    networks:
      - crawl-agent-network
    restart: unless-stopped
    profiles:
      - with-nginx

  # Prometheus for metrics (optional)
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - crawl-agent-network
    restart: unless-stopped
    profiles:
      - monitoring
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  # Grafana for visualization (optional)
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - crawl-agent-network
    restart: unless-stopped
    profiles:
      - monitoring

volumes:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  crawl-agent-network:
    driver: bridge
