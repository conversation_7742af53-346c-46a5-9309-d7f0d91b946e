version: '3.8'

services:
  # Main FastAPI application - Production
  app:
    build:
      context: ..
      dockerfile: docker/Dockerfile
      target: production
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
      - DEBUG=False
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    env_file:
      - ../.env.production
    volumes:
      - app_logs:/app/logs
      - app_data:/app/data
      - app_temp:/app/temp
      - app_uploads:/app/uploads
      - app_downloads:/app/downloads
    depends_on:
      - redis
    networks:
      - crawl-agent-network
    restart: unless-stopped
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis - Production with persistence
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf:ro
    networks:
      - crawl-agent-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    command: redis-server /usr/local/etc/redis/redis.conf

  # Celery worker - Production
  celery-worker:
    build:
      context: ..
      dockerfile: docker/Dockerfile
      target: production
    environment:
      - ENVIRONMENT=production
      - DEBUG=False
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    env_file:
      - ../.env.production
    volumes:
      - app_logs:/app/logs
      - app_data:/app/data
      - app_temp:/app/temp
      - app_uploads:/app/uploads
      - app_downloads:/app/downloads
    depends_on:
      - redis
    networks:
      - crawl-agent-network
    restart: unless-stopped
    deploy:
      replicas: 3
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    command: celery -A app.services.task_queue worker --loglevel=info --concurrency=4 --max-tasks-per-child=1000
    healthcheck:
      test: ["CMD", "celery", "-A", "app.services.task_queue", "inspect", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Celery beat - Production
  celery-beat:
    build:
      context: ..
      dockerfile: docker/Dockerfile
      target: production
    environment:
      - ENVIRONMENT=production
      - DEBUG=False
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    env_file:
      - ../.env.production
    volumes:
      - app_logs:/app/logs
      - app_data:/app/data
      - celery_beat_data:/app/celerybeat
    depends_on:
      - redis
    networks:
      - crawl-agent-network
    restart: unless-stopped
    deploy:
      replicas: 1
      resources:
        limits:
          cpus: '0.25'
          memory: 256M
        reservations:
          cpus: '0.1'
          memory: 128M
    command: celery -A app.services.task_queue beat --loglevel=info --pidfile=/app/celerybeat/celerybeat.pid --schedule=/app/celerybeat/celerybeat-schedule
    healthcheck:
      test: ["CMD", "celery", "-A", "app.services.task_queue", "inspect", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx Load Balancer and SSL Termination
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.prod.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - app
    networks:
      - crawl-agent-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.25'
          memory: 128M
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/api/v1/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus for metrics
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.prod.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - crawl-agent-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
      - '--storage.tsdb.wal-compression'

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD_FILE=/run/secrets/grafana_admin_password
      - GF_SECURITY_SECRET_KEY_FILE=/run/secrets/grafana_secret_key
      - GF_INSTALL_PLUGINS=grafana-piechart-panel
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - crawl-agent-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
    secrets:
      - grafana_admin_password
      - grafana_secret_key

  # Log aggregation with Fluentd (optional)
  fluentd:
    image: fluent/fluentd:v1.16-debian-1
    ports:
      - "24224:24224"
      - "24224:24224/udp"
    volumes:
      - ./fluentd/fluent.conf:/fluentd/etc/fluent.conf:ro
      - fluentd_logs:/var/log/fluentd
    networks:
      - crawl-agent-network
    restart: unless-stopped
    profiles:
      - logging

volumes:
  redis_data:
  prometheus_data:
  grafana_data:
  app_logs:
  app_data:
  app_temp:
  app_uploads:
  app_downloads:
  celery_beat_data:
  nginx_logs:
  fluentd_logs:

networks:
  crawl-agent-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

secrets:
  grafana_admin_password:
    file: ./secrets/grafana_admin_password.txt
  grafana_secret_key:
    file: ./secrets/grafana_secret_key.txt
