# 🔥 Firebase Quick Setup Guide

This is a quick guide to set up Firebase for Crawl Agent. For detailed instructions, see `docs/FIREBASE_SETUP.md`.

## Option 1: Interactive Setup (Recommended)

Run the interactive setup script:

```bash
python scripts/interactive_firebase_setup.py
```

This script will:
1. Guide you through loading your service account JSON
2. Help you configure the Web API key
3. Create your `.env` file automatically
4. Test the Firebase connection

## Option 2: Manual Setup

### Step 1: Create Firebase Project

1. Go to https://console.firebase.google.com
2. Click "Create a project"
3. Enter project name (e.g., "crawl-agent")
4. Enable Google Analytics (optional)
5. Click "Create project"

### Step 2: Enable Services

**Enable Authentication:**
1. Go to Authentication → Get started
2. Go to Sign-in method tab
3. Enable "Email/Password"

**Enable Firestore:**
1. Go to Firestore Database → Create database
2. Start in test mode (for development)
3. Choose a location

### Step 3: Get Service Account Credentials

1. Go to Project Settings (gear icon)
2. Go to Service accounts tab
3. Click "Generate new private key"
4. Download the JSON file
5. **Keep this file secure!**

### Step 4: Get Web API Key

1. Go to Project Settings → General tab
2. Scroll to "Your apps" section
3. If no web app exists, click "Add app" → Web
4. Copy the "Web API Key"

### Step 5: Configure Environment

1. **Copy the template:**
   ```bash
   cp .env.example .env
   ```

2. **Edit `.env` file with your Firebase values:**

   From your service account JSON file, copy these values:
   ```env
   FIREBASE_PROJECT_ID=your-project-id
   FIREBASE_PRIVATE_KEY_ID=your-private-key-id
   FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----\n"
   FIREBASE_CLIENT_EMAIL=*******
   FIREBASE_CLIENT_ID=your-client-id
   FIREBASE_WEB_API_KEY=your-web-api-key
   ```

   **Important:** 
   - Keep the quotes around the private key
   - Replace `\n` with actual newlines in the private key
   - Update the client cert URL with your service account email

3. **Generate a secure secret key:**
   ```bash
   python -c "import secrets; print(secrets.token_urlsafe(32))"
   ```
   Replace `your-super-secret-key-change-this-in-production` with the generated key.

### Step 6: Test Setup

```bash
# Test Firebase connection
python scripts/setup_firebase.py

# Or start the app and check health
make dev
curl http://localhost:8000/api/v1/health/firebase
```

## Quick Reference: Service Account JSON to .env Mapping

If you have a service account JSON file like this:
```json
{
  "type": "service_account",
  "project_id": "my-project-123",
  "private_key_id": "abc123...",
  "private_key": "-----BEGIN PRIVATE KEY-----\nMIIEvQ...\n-----END PRIVATE KEY-----\n",
  "client_email": "*******",
  "client_id": "*********...",
  "auth_uri": "https://accounts.google.com/o/oauth2/auth",
  "token_uri": "https://oauth2.googleapis.com/token",
  "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
  "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-xyz%40my-project-123.iam.gserviceaccount.com"
}
```

Map it to your `.env` file:
```env
FIREBASE_PROJECT_ID=my-project-123
FIREBASE_PRIVATE_KEY_ID=abc123...
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nMIIEvQ...\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=*******
FIREBASE_CLIENT_ID=*********...
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token
FIREBASE_AUTH_PROVIDER_X509_CERT_URL=https://www.googleapis.com/oauth2/v1/certs
FIREBASE_CLIENT_X509_CERT_URL=https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-xyz%40my-project-123.iam.gserviceaccount.com
FIREBASE_WEB_API_KEY=your-web-api-key-from-console
```

## Common Issues

### "Invalid private key" Error
- Ensure the private key includes `\n` for line breaks
- Keep the quotes around the entire private key
- Don't modify the key content

### "Project not found" Error
- Double-check the `FIREBASE_PROJECT_ID`
- Ensure the project exists in Firebase Console

### "Permission denied" Error
- Check Firestore security rules
- Ensure the service account has proper permissions
- Verify Authentication is enabled

## Next Steps

After setup:
1. Start the development server: `make dev`
2. Test the API: `curl http://localhost:8000/api/v1/health/`
3. Access docs: `http://localhost:8000/docs`
4. Set up Firestore security rules
5. Create your first user account

## Security Notes

- ⚠️ **Never commit your `.env` file**
- 🔒 Use different Firebase projects for dev/prod
- 🛡️ Set up proper Firestore security rules
- 🔑 Regularly rotate service account keys

## Need Help?

- 📚 Detailed guide: `docs/FIREBASE_SETUP.md`
- 🔧 Troubleshooting: `docs/TROUBLESHOOTING.md`
- 🚀 Interactive setup: `python scripts/interactive_firebase_setup.py`
