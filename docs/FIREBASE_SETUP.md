# Firebase Setup Guide

This guide will walk you through setting up Firebase for the Crawl Agent project and configuring your `.env` file.

## Step 1: Create Firebase Project

1. **Go to Firebase Console**
   - Visit https://console.firebase.google.com
   - Sign in with your Google account

2. **Create New Project**
   - Click "Create a project"
   - Enter project name (e.g., "crawl-agent-prod")
   - Choose whether to enable Google Analytics (optional)
   - Click "Create project"

## Step 2: Enable Required Services

### Enable Authentication
1. In Firebase Console, go to **Authentication** → **Get started**
2. Go to **Sign-in method** tab
3. Enable the following providers:
   - **Email/Password** (required)
   - **Google** (optional, recommended)
   - **Anonymous** (optional, for guest access)

### Enable Firestore Database
1. Go to **Firestore Database** → **Create database**
2. Choose **Start in test mode** (for development) or **Start in production mode**
3. Select a location (choose closest to your users)
4. Click "Done"

### Enable Storage (Optional)
1. Go to **Storage** → **Get started**
2. Choose security rules (start in test mode for development)
3. Select a location
4. Click "Done"

## Step 3: Create Service Account

1. **Go to Project Settings**
   - Click the gear icon → "Project settings"
   - Go to **Service accounts** tab

2. **Generate Private Key**
   - Click "Generate new private key"
   - Click "Generate key" to download the JSON file
   - **Keep this file secure!** It contains sensitive credentials

3. **Note the Service Account Details**
   - Service account email (ends with `@your-project.iam.gserviceaccount.com`)
   - Project ID
   - Private key ID

## Step 4: Get Web API Key

1. **Go to Project Settings**
   - Click the gear icon → "Project settings"
   - Go to **General** tab

2. **Find Web API Key**
   - Scroll down to "Your apps" section
   - If no web app exists, click "Add app" → Web app
   - Copy the "Web API Key" from the config object

## Step 5: Configure Environment Variables

### Copy Environment Template
```bash
cp .env.example .env
```

### Edit .env File
Open the `.env` file and replace the Firebase configuration values:

```env
# Firebase Configuration
FIREBASE_PROJECT_ID=your-actual-project-id
FIREBASE_PRIVATE_KEY_ID=your-actual-private-key-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_ACTUAL_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=*******
FIREBASE_CLIENT_ID=your-actual-client-id
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token
FIREBASE_AUTH_PROVIDER_X509_CERT_URL=https://www.googleapis.com/oauth2/v1/certs
FIREBASE_CLIENT_X509_CERT_URL=https://www.googleapis.com/robot/v1/metadata/x509/your-service-account%40your-project.iam.gserviceaccount.com
FIREBASE_WEB_API_KEY=your-actual-web-api-key
```

### Important Notes for Private Key
- The private key must include the `\n` characters for line breaks
- Keep the quotes around the entire private key
- The key should start with `-----BEGIN PRIVATE KEY-----\n` and end with `\n-----END PRIVATE KEY-----\n`

## Step 6: Extract Values from Service Account JSON

If you have the service account JSON file, here's how to extract the values:

### Sample Service Account JSON Structure
```json
{
  "type": "service_account",
  "project_id": "your-project-id",
  "private_key_id": "your-private-key-id",
  "private_key": "-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...\n-----END PRIVATE KEY-----\n",
  "client_email": "*******",
  "client_id": "your-client-id",
  "auth_uri": "https://accounts.google.com/o/oauth2/auth",
  "token_uri": "https://oauth2.googleapis.com/token",
  "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
  "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/your-service-account%40your-project.iam.gserviceaccount.com"
}
```

### Mapping to Environment Variables
- `project_id` → `FIREBASE_PROJECT_ID`
- `private_key_id` → `FIREBASE_PRIVATE_KEY_ID`
- `private_key` → `FIREBASE_PRIVATE_KEY`
- `client_email` → `FIREBASE_CLIENT_EMAIL`
- `client_id` → `FIREBASE_CLIENT_ID`
- `auth_uri` → `FIREBASE_AUTH_URI`
- `token_uri` → `FIREBASE_TOKEN_URI`
- `auth_provider_x509_cert_url` → `FIREBASE_AUTH_PROVIDER_X509_CERT_URL`
- `client_x509_cert_url` → `FIREBASE_CLIENT_X509_CERT_URL`

## Step 7: Set Up Firestore Security Rules

### Development Rules (Permissive)
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow read/write access to all documents for authenticated users
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

### Production Rules (Restrictive)
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Users can only access their own scraping jobs
    match /scraping_jobs/{jobId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.user_id;
    }
    
    // Admin users can access all data
    match /{document=**} {
      allow read, write: if request.auth != null && 
        request.auth.token.admin == true;
    }
  }
}
```

## Step 8: Test Firebase Connection

Run the Firebase setup script to test your configuration:

```bash
python scripts/setup_firebase.py
```

Or test manually:
```bash
# Test health endpoint
curl http://localhost:8000/api/v1/health/firebase

# Start the application
make dev

# Check logs for Firebase connection status
```

## Step 9: Create Initial Admin User (Optional)

You can create an admin user through the Firebase Console:

1. Go to **Authentication** → **Users**
2. Click "Add user"
3. Enter email and password
4. After creation, click on the user
5. Go to **Custom claims** and add:
   ```json
   {
     "admin": true,
     "role": "admin"
   }
   ```

## Common Issues and Solutions

### Issue: "Invalid private key"
**Solution**: Ensure the private key includes proper line breaks (`\n`) and is wrapped in quotes.

### Issue: "Project not found"
**Solution**: Double-check the `FIREBASE_PROJECT_ID` matches your Firebase project ID exactly.

### Issue: "Permission denied"
**Solution**: 
- Check Firestore security rules
- Ensure the service account has proper permissions
- Verify the user is authenticated

### Issue: "Service account not found"
**Solution**: 
- Regenerate the service account key
- Ensure the service account email is correct
- Check that the service account hasn't been deleted

## Security Best Practices

1. **Never commit the `.env` file** - It's already in `.gitignore`
2. **Use different Firebase projects** for development and production
3. **Regularly rotate service account keys**
4. **Set up proper Firestore security rules**
5. **Enable Firebase App Check** for production
6. **Monitor Firebase usage** and set up billing alerts

## Environment-Specific Configuration

### Development
```env
ENVIRONMENT=development
DEBUG=True
FIREBASE_PROJECT_ID=your-dev-project-id
```

### Production
```env
ENVIRONMENT=production
DEBUG=False
FIREBASE_PROJECT_ID=your-prod-project-id
```

## Next Steps

After setting up Firebase:

1. **Test the connection** using the health check endpoint
2. **Create test users** through the authentication system
3. **Set up proper security rules** for your use case
4. **Configure monitoring** and alerts
5. **Set up backup procedures** for Firestore data

## Support

If you encounter issues:
1. Check the Firebase Console for error messages
2. Review the application logs
3. Test the connection using the provided scripts
4. Consult the Firebase documentation
5. Check the troubleshooting guide in `docs/TROUBLESHOOTING.md`
