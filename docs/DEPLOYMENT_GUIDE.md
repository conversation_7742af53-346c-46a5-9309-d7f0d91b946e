# Deployment Guide - Crawl Agent

This guide covers deploying Crawl Agent to various environments, from development to production.

## Prerequisites

### System Requirements
- **CPU**: 2+ cores recommended
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 20GB minimum, SSD recommended
- **Network**: Stable internet connection

### Software Requirements
- Docker 20.10+
- Docker Compose 2.0+
- Git
- Make (optional, for convenience commands)

### External Services
- Firebase project with Firestore and Authentication enabled
- Redis instance (can be containerized)
- Domain name and SSL certificate (for production)

## Environment Setup

### 1. Clone Repository
```bash
git clone <repository-url>
cd crawl-agent
```

### 2. Environment Configuration
```bash
# Copy environment template
cp .env.example .env

# Edit configuration
nano .env
```

### 3. Firebase Setup
1. Create a Firebase project at https://console.firebase.google.com
2. Enable Authentication and Firestore Database
3. Create a service account and download the JSON key
4. Extract credentials and add to `.env` file

## Development Deployment

### Using Docker Compose (Recommended)
```bash
# Build and start all services
make docker-run

# Or manually
docker-compose -f docker/docker-compose.yml up -d
```

### Manual Setup
```bash
# Install dependencies
pip install -r requirements.txt
playwright install chromium

# Start Redis (if not using Docker)
redis-server

# Start Celery worker (in separate terminal)
celery -A app.services.task_queue.celery_app worker --loglevel=info

# Start development server
make dev
```

### Verify Development Setup
```bash
# Check health
curl http://localhost:8000/api/v1/health/

# Access API documentation
open http://localhost:8000/docs
```

## Production Deployment

### Docker Compose Production
```bash
# Build production image
make docker-build-prod

# Start production services
make docker-run-prod

# Or manually
docker-compose -f docker/docker-compose.prod.yml up -d
```

### Manual Production Setup
```bash
# Install production dependencies
pip install -r requirements.txt
playwright install chromium

# Set production environment
export ENVIRONMENT=production
export DEBUG=False

# Start with Gunicorn
gunicorn app.main:app \
  -w 4 \
  -k uvicorn.workers.UvicornWorker \
  --bind 0.0.0.0:8000 \
  --access-logfile - \
  --error-logfile -
```

## Cloud Deployment

### Google Cloud Platform (GCP)

#### Using Cloud Run
```bash
# Build and push image
gcloud builds submit --tag gcr.io/PROJECT_ID/crawl-agent

# Deploy to Cloud Run
gcloud run deploy crawl-agent \
  --image gcr.io/PROJECT_ID/crawl-agent \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --memory 2Gi \
  --cpu 2 \
  --max-instances 10
```

#### Using Google Kubernetes Engine (GKE)
```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: crawl-agent
spec:
  replicas: 3
  selector:
    matchLabels:
      app: crawl-agent
  template:
    metadata:
      labels:
        app: crawl-agent
    spec:
      containers:
      - name: crawl-agent
        image: gcr.io/PROJECT_ID/crawl-agent:latest
        ports:
        - containerPort: 8000
        env:
        - name: ENVIRONMENT
          value: "production"
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /api/v1/health/live
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/v1/health/ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
```

### Amazon Web Services (AWS)

#### Using ECS Fargate
```json
{
  "family": "crawl-agent",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "1024",
  "memory": "2048",
  "executionRoleArn": "arn:aws:iam::ACCOUNT:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "crawl-agent",
      "image": "your-account.dkr.ecr.region.amazonaws.com/crawl-agent:latest",
      "portMappings": [
        {
          "containerPort": 8000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "ENVIRONMENT",
          "value": "production"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/crawl-agent",
          "awslogs-region": "us-west-2",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ]
}
```

#### Using Elastic Beanstalk
```yaml
# .ebextensions/01_packages.config
packages:
  yum:
    git: []
    docker: []

# Dockerrun.aws.json
{
  "AWSEBDockerrunVersion": "1",
  "Image": {
    "Name": "your-account.dkr.ecr.region.amazonaws.com/crawl-agent:latest",
    "Update": "true"
  },
  "Ports": [
    {
      "ContainerPort": "8000"
    }
  ]
}
```

### Microsoft Azure

#### Using Container Instances
```bash
# Create resource group
az group create --name crawl-agent-rg --location eastus

# Deploy container
az container create \
  --resource-group crawl-agent-rg \
  --name crawl-agent \
  --image your-registry.azurecr.io/crawl-agent:latest \
  --cpu 2 \
  --memory 4 \
  --ports 8000 \
  --environment-variables ENVIRONMENT=production
```

#### Using App Service
```bash
# Create App Service plan
az appservice plan create \
  --name crawl-agent-plan \
  --resource-group crawl-agent-rg \
  --sku B1 \
  --is-linux

# Create web app
az webapp create \
  --resource-group crawl-agent-rg \
  --plan crawl-agent-plan \
  --name crawl-agent-app \
  --deployment-container-image-name your-registry.azurecr.io/crawl-agent:latest
```

## Kubernetes Deployment

### Complete Kubernetes Manifests

#### Namespace
```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: crawl-agent
```

#### ConfigMap
```yaml
# k8s/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: crawl-agent-config
  namespace: crawl-agent
data:
  ENVIRONMENT: "production"
  DEBUG: "False"
  HOST: "0.0.0.0"
  PORT: "8000"
```

#### Secret
```yaml
# k8s/secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: crawl-agent-secrets
  namespace: crawl-agent
type: Opaque
data:
  SECRET_KEY: <base64-encoded-secret>
  FIREBASE_PRIVATE_KEY: <base64-encoded-key>
```

#### Deployment
```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: crawl-agent
  namespace: crawl-agent
spec:
  replicas: 3
  selector:
    matchLabels:
      app: crawl-agent
  template:
    metadata:
      labels:
        app: crawl-agent
    spec:
      containers:
      - name: crawl-agent
        image: crawl-agent:latest
        ports:
        - containerPort: 8000
        envFrom:
        - configMapRef:
            name: crawl-agent-config
        - secretRef:
            name: crawl-agent-secrets
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /api/v1/health/live
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/v1/health/ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
```

#### Service
```yaml
# k8s/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: crawl-agent-service
  namespace: crawl-agent
spec:
  selector:
    app: crawl-agent
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8000
  type: ClusterIP
```

#### Ingress
```yaml
# k8s/ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: crawl-agent-ingress
  namespace: crawl-agent
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  tls:
  - hosts:
    - api.yourdomain.com
    secretName: crawl-agent-tls
  rules:
  - host: api.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: crawl-agent-service
            port:
              number: 80
```

#### Horizontal Pod Autoscaler
```yaml
# k8s/hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: crawl-agent-hpa
  namespace: crawl-agent
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: crawl-agent
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

### Deploy to Kubernetes
```bash
# Apply all manifests
kubectl apply -f k8s/

# Check deployment status
kubectl get pods -n crawl-agent
kubectl get services -n crawl-agent
kubectl get ingress -n crawl-agent

# View logs
kubectl logs -f deployment/crawl-agent -n crawl-agent
```

## SSL/TLS Configuration

### Using Let's Encrypt with Nginx
```nginx
# /etc/nginx/sites-available/crawl-agent
server {
    listen 80;
    server_name api.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name api.yourdomain.com;

    ssl_certificate /etc/letsencrypt/live/api.yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/api.yourdomain.com/privkey.pem;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### Obtain SSL Certificate
```bash
# Install Certbot
sudo apt-get install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d api.yourdomain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## Monitoring and Logging

### Prometheus Configuration
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'crawl-agent'
    static_configs:
      - targets: ['localhost:8000']
    metrics_path: '/metrics'
```

### Grafana Dashboard
```json
{
  "dashboard": {
    "title": "Crawl Agent Metrics",
    "panels": [
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])"
          }
        ]
      },
      {
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))"
          }
        ]
      }
    ]
  }
}
```

### Log Aggregation with ELK Stack
```yaml
# docker-compose.monitoring.yml
version: '3.8'
services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.15.0
    environment:
      - discovery.type=single-node
    ports:
      - "9200:9200"

  logstash:
    image: docker.elastic.co/logstash/logstash:7.15.0
    volumes:
      - ./logstash.conf:/usr/share/logstash/pipeline/logstash.conf
    ports:
      - "5000:5000"

  kibana:
    image: docker.elastic.co/kibana/kibana:7.15.0
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
```

## Backup and Recovery

### Database Backup
```bash
# Firestore backup (using gcloud)
gcloud firestore export gs://your-backup-bucket/firestore-backup-$(date +%Y%m%d)

# Automated backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
gcloud firestore export gs://your-backup-bucket/firestore-backup-$DATE
```

### Application Data Backup
```bash
# Backup uploaded files
tar -czf backup-files-$(date +%Y%m%d).tar.gz uploads/ downloads/ data/

# Backup logs
tar -czf backup-logs-$(date +%Y%m%d).tar.gz logs/
```

### Recovery Procedures
```bash
# Restore Firestore
gcloud firestore import gs://your-backup-bucket/firestore-backup-20240101/

# Restore application files
tar -xzf backup-files-20240101.tar.gz
```

## Performance Optimization

### Application Tuning
```python
# app/config/settings.py
class Settings(BaseSettings):
    # Worker configuration
    workers: int = 4
    worker_connections: int = 1000
    
    # Connection pooling
    max_connections: int = 100
    connection_timeout: int = 30
    
    # Caching
    cache_ttl: int = 3600
    redis_max_connections: int = 50
```

### Database Optimization
```python
# Firestore indexes
# Create composite indexes for common queries
# Index on: user_id, status, created_at
# Index on: user_id, tags, created_at
```

### Caching Strategy
```python
# Redis caching configuration
CACHE_CONFIG = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": "redis://127.0.0.1:6379/1",
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
        }
    }
}
```

## Security Hardening

### Application Security
```python
# app/core/security.py
SECURITY_HEADERS = {
    "X-Content-Type-Options": "nosniff",
    "X-Frame-Options": "DENY",
    "X-XSS-Protection": "1; mode=block",
    "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
    "Content-Security-Policy": "default-src 'self'",
    "Referrer-Policy": "strict-origin-when-cross-origin"
}
```

### Network Security
```bash
# Firewall rules (UFW)
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

### Container Security
```dockerfile
# Use non-root user
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

USER appuser
```

## Troubleshooting

### Common Issues
1. **Container won't start**: Check environment variables and Firebase credentials
2. **High memory usage**: Adjust worker count and connection limits
3. **Slow scraping**: Optimize delay settings and concurrent requests
4. **Database connection errors**: Check Firebase configuration and network connectivity

### Debug Commands
```bash
# Check container logs
docker logs crawl-agent-app

# Check resource usage
docker stats

# Test connectivity
curl -f http://localhost:8000/api/v1/health/

# Check Firebase connection
python scripts/test_firebase.py
```

### Health Monitoring
```bash
# Automated health check script
#!/bin/bash
HEALTH_URL="http://localhost:8000/api/v1/health/"
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" $HEALTH_URL)

if [ $RESPONSE -ne 200 ]; then
    echo "Health check failed with status: $RESPONSE"
    # Send alert or restart service
fi
```

## Maintenance

### Regular Tasks
- Monitor disk usage and clean up old logs
- Update dependencies and security patches
- Review and rotate API keys
- Backup database and application data
- Monitor performance metrics and optimize

### Update Procedure
```bash
# Pull latest changes
git pull origin main

# Rebuild and deploy
make docker-build-prod
make docker-run-prod

# Verify deployment
make health
```

This deployment guide provides comprehensive instructions for deploying Crawl Agent in various environments. Choose the deployment method that best fits your infrastructure and requirements.
