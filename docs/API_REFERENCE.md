# API Reference - Crawl Agent

This document provides detailed information about all API endpoints available in the Crawl Agent platform.

## Base URL
- **Development**: `http://localhost:8000`
- **Production**: `https://your-domain.com`

## Authentication

Most endpoints require authentication using Firebase ID tokens or JWT tokens.

### Headers
```http
Authorization: Bearer <token>
Content-Type: application/json
```

### API Key Authentication (Alternative)
```http
X-API-Key: <your-api-key>
Content-Type: application/json
```

## Response Format

All API responses follow a consistent format:

### Success Response
```json
{
  "data": { ... },
  "message": "Success message",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### Error Response
```json
{
  "error": "ERROR_CODE",
  "message": "Human readable error message",
  "timestamp": "2024-01-01T00:00:00Z",
  "details": { ... }
}
```

## Authentication Endpoints

### Register User
**POST** `/api/v1/auth/register`

Register a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword123",
  "display_name": "John Doe"
}
```

**Response:**
```json
{
  "message": "User registered successfully",
  "user_id": "user-uuid",
  "email_verification_sent": true
}
```

### Login
**POST** `/api/v1/auth/login`

Authenticate user and return access token.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword123"
}
```

**Response:**
```json
{
  "access_token": "jwt-token",
  "token_type": "bearer",
  "expires_in": 1800,
  "user": {
    "uid": "user-uuid",
    "email": "<EMAIL>",
    "display_name": "John Doe",
    "role": "user",
    "status": "active"
  }
}
```

### Get Current User
**GET** `/api/v1/auth/me`

Get current user profile information.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "uid": "user-uuid",
  "email": "<EMAIL>",
  "display_name": "John Doe",
  "role": "user",
  "status": "active",
  "created_at": "2024-01-01T00:00:00Z",
  "email_verified": true,
  "total_scraping_jobs": 15,
  "api_calls_this_month": 250
}
```

## Web Scraping Endpoints

### Quick Scrape
**POST** `/api/v1/scrape/quick`

Perform immediate scraping of a single URL.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "url": "https://example.com",
  "extract_text": true,
  "extract_links": false,
  "extract_images": false,
  "javascript": true,
  "clean_data": true
}
```

**Response:**
```json
{
  "url": "https://example.com",
  "title": "Example Page",
  "content": "Cleaned page content...",
  "links": ["https://example.com/link1"],
  "images": ["https://example.com/image1.jpg"],
  "metadata": {
    "title": "Example Page",
    "description": "Page description"
  },
  "processing_time": 2.5,
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### Create Scraping Job
**POST** `/api/v1/scrape/jobs`

Create a background scraping job for multiple URLs or complex configurations.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "name": "My Scraping Job",
  "description": "Scrape product information",
  "config": {
    "urls": ["https://example.com/page1", "https://example.com/page2"],
    "max_depth": 2,
    "max_pages": 50,
    "delay": 1.0,
    "timeout": 30,
    "javascript": true,
    "extraction_strategy": "css",
    "css_selectors": {
      "title": "h1",
      "price": ".price",
      "description": ".description"
    },
    "clean_data": true,
    "output_format": "json"
  },
  "priority": "normal",
  "tags": ["products", "ecommerce"]
}
```

**Response:**
```json
{
  "job_id": "job-uuid",
  "message": "Scraping job created successfully",
  "status": "pending",
  "estimated_completion": "2024-01-01T00:05:00Z"
}
```

### List Scraping Jobs
**GET** `/api/v1/scrape/jobs`

List user's scraping jobs with filtering and pagination.

**Headers:** `Authorization: Bearer <token>`

**Query Parameters:**
- `status` (optional): Filter by job status (pending, running, completed, failed)
- `page` (default: 1): Page number
- `page_size` (default: 20): Items per page

**Response:**
```json
{
  "jobs": [
    {
      "job_id": "job-uuid",
      "name": "My Scraping Job",
      "status": "completed",
      "priority": "normal",
      "progress_percentage": 100.0,
      "success_rate": 95.0,
      "total_urls": 50,
      "processed_urls": 50,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:05:00Z",
      "tags": ["products", "ecommerce"]
    }
  ],
  "total": 1,
  "page": 1,
  "page_size": 20,
  "has_next": false
}
```

### Get Job Details
**GET** `/api/v1/scrape/jobs/{job_id}`

Get detailed information about a specific scraping job.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "job_id": "job-uuid",
  "user_id": "user-uuid",
  "name": "My Scraping Job",
  "description": "Scrape product information",
  "status": "completed",
  "priority": "normal",
  "config": { ... },
  "results": [
    {
      "url": "https://example.com/page1",
      "title": "Product 1",
      "content": "Product description...",
      "extracted_data": {
        "title": "Product 1",
        "price": "$29.99",
        "description": "Great product..."
      },
      "timestamp": "2024-01-01T00:01:00Z",
      "duration": 1.2
    }
  ],
  "stats": {
    "total_urls": 50,
    "processed_urls": 50,
    "successful_urls": 47,
    "failed_urls": 3,
    "average_processing_time": 1.5,
    "total_duration": 75.0
  },
  "created_at": "2024-01-01T00:00:00Z",
  "completed_at": "2024-01-01T00:05:00Z"
}
```

### Cancel Job
**POST** `/api/v1/scrape/jobs/{job_id}/cancel`

Cancel a running or pending scraping job.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "message": "Job cancelled successfully"
}
```

## Data Management Endpoints

### Get Job Results
**GET** `/api/v1/data/jobs/{job_id}/results`

Retrieve results from a completed scraping job.

**Headers:** `Authorization: Bearer <token>`

**Query Parameters:**
- `format` (default: json): Output format (json, csv, xlsx, xml)
- `page` (default: 1): Page number for pagination
- `page_size` (default: 100): Items per page

**Response:**
```json
{
  "job_id": "job-uuid",
  "results": [
    {
      "url": "https://example.com/page1",
      "title": "Product 1",
      "extracted_data": { ... },
      "timestamp": "2024-01-01T00:01:00Z"
    }
  ],
  "total": 50,
  "page": 1,
  "page_size": 100,
  "has_next": false
}
```

### Export Data
**POST** `/api/v1/data/export`

Export job data in specified format for download.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "job_id": "job-uuid",
  "format": "csv",
  "include_metadata": true,
  "flatten_nested": false
}
```

**Response:**
```json
{
  "export_id": "export-uuid",
  "download_url": "/api/v1/data/download/export-uuid",
  "format": "csv",
  "file_size_mb": 2.5,
  "expires_at": "2024-01-01T01:00:00Z"
}
```

### Get Data Statistics
**GET** `/api/v1/data/stats`

Get user's data usage statistics.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "total_jobs": 25,
  "total_data_points": 1250,
  "total_storage_mb": 45.2,
  "successful_jobs": 23,
  "failed_jobs": 2,
  "last_updated": "2024-01-01T00:00:00Z"
}
```

### Search Data
**POST** `/api/v1/data/search`

Search through scraped data using full-text search.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "query": "product price",
  "job_ids": ["job-uuid-1", "job-uuid-2"],
  "date_from": "2024-01-01T00:00:00Z",
  "date_to": "2024-01-31T23:59:59Z",
  "limit": 100
}
```

**Response:**
```json
{
  "results": [
    {
      "job_id": "job-uuid",
      "url": "https://example.com/page1",
      "title": "Product 1",
      "content": "Matching content...",
      "score": 0.95
    }
  ],
  "total_matches": 15,
  "search_time_ms": 45.2
}
```

## Health Check Endpoints

### Comprehensive Health Check
**GET** `/api/v1/health/`

Get comprehensive health status of all services.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00Z",
  "version": "1.0.0",
  "environment": "production",
  "uptime_seconds": 86400.0,
  "checks": {
    "firebase": {
      "status": "healthy",
      "response_time_ms": 25.3,
      "message": "Firebase connection successful"
    },
    "scraper": {
      "status": "healthy",
      "response_time_ms": 150.7,
      "message": "Scraper service operational"
    },
    "system": {
      "status": "healthy",
      "response_time_ms": 5.1,
      "message": "System resources normal",
      "details": {
        "cpu_percent": 45.2,
        "memory_percent": 62.1,
        "disk_percent": 35.8
      }
    }
  }
}
```

### Liveness Probe
**GET** `/api/v1/health/live`

Simple liveness check for Kubernetes.

**Response:**
```json
{
  "status": "alive",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### Readiness Probe
**GET** `/api/v1/health/ready`

Readiness check for Kubernetes.

**Response:**
```json
{
  "status": "ready",
  "timestamp": "2024-01-01T00:00:00Z",
  "checks": {
    "firebase": "healthy"
  }
}
```

## Error Codes

| Code | Description |
|------|-------------|
| `AUTHENTICATION_ERROR` | Authentication failed or token invalid |
| `AUTHORIZATION_ERROR` | Insufficient permissions |
| `VALIDATION_ERROR` | Request data validation failed |
| `NOT_FOUND_ERROR` | Requested resource not found |
| `CONFLICT_ERROR` | Resource conflict (e.g., duplicate email) |
| `RATE_LIMIT_ERROR` | Rate limit exceeded |
| `SCRAPING_ERROR` | Web scraping operation failed |
| `DATA_PROCESSING_ERROR` | Data processing failed |
| `FIREBASE_ERROR` | Firebase operation failed |
| `EXTERNAL_SERVICE_ERROR` | External service unavailable |

## Rate Limits

| Endpoint Category | Limit | Window |
|------------------|-------|---------|
| Authentication | 5 requests | 1 minute |
| General API | 60 requests | 1 minute |
| Quick Scrape | 10 requests | 1 minute |
| Job Creation | 5 requests | 1 minute |
| Data Export | 3 requests | 1 minute |

## Pagination

List endpoints support pagination with the following parameters:
- `page`: Page number (1-based, default: 1)
- `page_size`: Items per page (1-100, default: 20)

Pagination response includes:
- `total`: Total number of items
- `page`: Current page number
- `page_size`: Items per page
- `has_next`: Whether there are more pages

## Webhooks (Future Feature)

Webhook endpoints for job completion notifications:
- `POST /api/v1/webhooks/job-completed` - Job completion webhook
- `POST /api/v1/webhooks/job-failed` - Job failure webhook

## SDK and Client Libraries

Official client libraries (planned):
- Python SDK
- JavaScript/Node.js SDK
- Go SDK
- REST API collections for Postman/Insomnia
