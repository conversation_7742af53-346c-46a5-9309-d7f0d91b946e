# Crawl Agent Documentation

Welcome to the Crawl Agent documentation! This directory contains comprehensive guides and references for the Crawl Agent web scraping and data extraction platform.

## 📚 Documentation Index

### Getting Started
- **[PROJECT_OVERVIEW.md](./PROJECT_OVERVIEW.md)** - Complete project overview, features, and architecture
- **[DEVELOPMENT_GUIDE.md](./DEVELOPMENT_GUIDE.md)** - Development setup, guidelines, and best practices
- **[DEPLOYMENT_GUIDE.md](./DEPLOYMENT_GUIDE.md)** - Production deployment instructions for various platforms

### API Documentation
- **[API_REFERENCE.md](./API_REFERENCE.md)** - Detailed API endpoint documentation with examples
- **[CONFIGURATION.md](./CONFIGURATION.md)** - Configuration options and environment variables
- **[TROUBLESHOOTING.md](./TROUBLESHOOTING.md)** - Common issues and solutions

## 🚀 Quick Start

1. **Read the Project Overview** - Start with [PROJECT_OVERVIEW.md](./PROJECT_OVERVIEW.md) to understand the platform
2. **Set up Development** - Follow [DEVELOPMENT_GUIDE.md](./DEVELOPMENT_GUIDE.md) for local development
3. **Explore the API** - Check [API_REFERENCE.md](./API_REFERENCE.md) for endpoint details
4. **Deploy to Production** - Use [DEPLOYMENT_GUIDE.md](./DEPLOYMENT_GUIDE.md) for deployment

## 🏗️ Architecture Overview

Crawl Agent is a modern, production-ready web scraping platform built with:

- **FastAPI** - Modern Python web framework
- **Crawl4AI** - AI-powered web scraping engine
- **Firebase** - Authentication and database
- **Docker** - Containerization and deployment
- **Celery** - Background task processing
- **Redis** - Caching and task queue

## 🌟 Key Features

- **Dynamic Web Scraping** - JavaScript-enabled scraping with Playwright
- **AI-Powered Extraction** - Intelligent content extraction using LLMs
- **Background Processing** - Async job processing for large-scale scraping
- **Data Processing** - Advanced cleaning, transformation, and validation
- **Production Ready** - Comprehensive monitoring, logging, and security
- **Scalable Architecture** - Horizontal scaling with Docker and Kubernetes

## 📋 API Endpoints Overview

### Authentication
- `POST /api/v1/auth/login` - User authentication
- `GET /api/v1/auth/me` - Get user profile

### Web Scraping
- `POST /api/v1/scrape/quick` - Quick single URL scraping
- `POST /api/v1/scrape/jobs` - Create background scraping jobs
- `GET /api/v1/scrape/jobs` - List scraping jobs

### Data Management
- `GET /api/v1/data/jobs/{job_id}/results` - Get job results
- `POST /api/v1/data/export` - Export data in various formats
- `POST /api/v1/data/search` - Search scraped data

### Health Checks
- `GET /api/v1/health/` - Comprehensive health check
- `GET /api/v1/health/live` - Liveness probe
- `GET /api/v1/health/ready` - Readiness probe

## 🛠️ Development Commands

```bash
# Setup development environment
make setup

# Run development server
make dev

# Run tests
make test

# Run with Docker
make docker-run

# View API documentation
open http://localhost:8000/docs
```

## 🐳 Docker Quick Start

```bash
# Clone repository
git clone <repository-url>
cd crawl-agent

# Copy environment template
cp .env.example .env
# Edit .env with your Firebase credentials

# Run with Docker Compose
make docker-run

# Check health
curl http://localhost:8000/api/v1/health/
```

## 🔧 Configuration

Key environment variables:

```env
# Application
APP_NAME=Crawl Agent
DEBUG=True
ENVIRONMENT=development

# Firebase
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>

# Redis
REDIS_URL=redis://localhost:6379/0

# Scraping
CRAWL4AI_MAX_CONCURRENT_REQUESTS=10
CRAWL4AI_REQUEST_TIMEOUT=30
```

## 🧪 Testing

The project includes comprehensive tests:

```bash
# Run all tests
make test

# Run with coverage
make test-cov

# Run specific test file
pytest tests/test_api/test_health.py -v
```

## 📊 Monitoring

### Health Checks
- **Liveness**: `/api/v1/health/live` - Application is running
- **Readiness**: `/api/v1/health/ready` - Application is ready to serve
- **Comprehensive**: `/api/v1/health/` - Detailed service health

### Metrics
- Request/response metrics
- Scraping job statistics
- System resource usage
- Error rates and performance

### Logging
- Structured JSON logging
- Request/response logging
- Error tracking
- Performance monitoring

## 🔒 Security

### Authentication
- Firebase Authentication integration
- JWT token-based authentication
- Role-based access control
- API key authentication

### Security Features
- Rate limiting
- CORS protection
- Security headers
- Input validation
- Error handling

## 🚀 Deployment Options

### Development
- Local development with Docker Compose
- Hot reload and debugging support

### Production
- Docker containers with multi-stage builds
- Kubernetes deployment manifests
- Cloud platform support (GCP, AWS, Azure)
- Load balancing and auto-scaling

## 📈 Scaling

### Horizontal Scaling
- Multiple application instances
- Load balancing with Nginx
- Kubernetes horizontal pod autoscaling

### Performance Optimization
- Redis caching
- Connection pooling
- Background task processing
- Database optimization

## 🤝 Contributing

1. Read the [DEVELOPMENT_GUIDE.md](./DEVELOPMENT_GUIDE.md)
2. Fork the repository
3. Create a feature branch
4. Make your changes with tests
5. Submit a pull request

## 📞 Support

- **Documentation**: Check the guides in this directory
- **Issues**: GitHub Issues for bug reports
- **Discussions**: GitHub Discussions for questions
- **API Documentation**: http://localhost:8000/docs (when running)

## 📄 License

This project is licensed under the MIT License. See the LICENSE file for details.

---

## 📖 Document Structure

```
docs/
├── README.md                 # This file - documentation index
├── PROJECT_OVERVIEW.md       # Complete project overview and features
├── API_REFERENCE.md          # Detailed API documentation
├── DEPLOYMENT_GUIDE.md       # Production deployment guide
├── DEVELOPMENT_GUIDE.md      # Development setup and guidelines
├── CONFIGURATION.md          # Configuration options and settings
└── TROUBLESHOOTING.md        # Common issues and solutions
```

Each document is self-contained but cross-references related information. Start with the PROJECT_OVERVIEW.md for a complete understanding of the platform, then dive into specific areas based on your needs.

**Happy scraping! 🕷️**
