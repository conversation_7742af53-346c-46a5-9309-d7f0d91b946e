# Core FastAPI and ASGI
fastapi>=0.110.0
uvicorn[standard]>=0.27.0
gunicorn>=21.2.0

# Firebase Integration
firebase-admin>=6.2.0
google-cloud-firestore>=2.13.1
google-cloud-storage>=2.10.0

# Web Scraping and AI
crawl4ai>=0.7.4
beautifulsoup4>=4.12.2
requests>=2.31.0

# Data Processing
pandas>=2.1.4
pydantic>=2.10.0
pydantic-settings>=2.1.0
python-multipart>=0.0.6

# Authentication and Security
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
itsdangerous>=2.1.2

# Database and Caching
redis>=5.0.1
aioredis>=2.0.1

# Background Tasks
celery>=5.3.4
flower>=2.0.1

# Monitoring and Logging
structlog>=23.2.0
prometheus-client>=0.19.0
sentry-sdk[fastapi]>=1.38.0

# Development and Testing
pytest>=7.4.3
pytest-asyncio>=0.21.1
pytest-cov>=4.1.0
factory-boy>=3.3.0
faker>=20.1.0

# Code Quality
black>=23.11.0
isort>=5.12.0
flake8>=6.1.0
mypy>=1.7.1
pre-commit>=3.6.0

# Environment and Configuration
python-dotenv>=1.0.0
pyyaml>=6.0.1

# Utilities
python-slugify>=8.0.1
email-validator>=2.1.0
phonenumbers>=8.13.26

# AI and ML (optional)
openai>=1.50.0
anthropic>=0.40.0
langchain>=0.3.0

# Async utilities
aiofiles>=24.1.0

# File handling
openpyxl>=3.1.2
xlsxwriter>=3.1.9
