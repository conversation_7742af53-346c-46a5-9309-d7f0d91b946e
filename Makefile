# Makefile for Crawl Agent

.PHONY: help install dev test lint format clean build run docker-build docker-run docker-stop setup-firebase

# Default target
help:
	@echo "Available commands:"
	@echo "  install        Install dependencies"
	@echo "  dev            Run development server"
	@echo "  test           Run tests"
	@echo "  test-cov       Run tests with coverage"
	@echo "  lint           Run linting"
	@echo "  format         Format code"
	@echo "  clean          Clean up temporary files"
	@echo "  build          Build the application"
	@echo "  run            Run production server"
	@echo "  docker-build   Build Docker image"
	@echo "  docker-run     Run with Docker Compose"
	@echo "  docker-stop    Stop Docker containers"
	@echo "  setup-firebase Setup Firebase configuration"

# Development setup
install:
	pip install -r requirements.txt
	playwright install chromium

dev:
	uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# Testing
test:
	pytest tests/ -v

test-cov:
	pytest tests/ -v --cov=app --cov-report=html --cov-report=term

test-watch:
	pytest-watch tests/ -- -v

# Code quality
lint:
	flake8 app tests
	mypy app

format:
	black app tests
	isort app tests

format-check:
	black --check app tests
	isort --check-only app tests

# Cleanup
clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf .coverage htmlcov/ .pytest_cache/ .mypy_cache/
	rm -rf build/ dist/

# Build and run
build:
	python -m build

run:
	gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000

# Docker commands
docker-build:
	docker build -f docker/Dockerfile -t crawl-agent .

docker-build-prod:
	docker build -f docker/Dockerfile --target production -t crawl-agent:prod .

docker-run:
	docker-compose -f docker/docker-compose.yml up -d

docker-run-prod:
	docker-compose -f docker/docker-compose.prod.yml up -d

docker-stop:
	docker-compose -f docker/docker-compose.yml down
	docker-compose -f docker/docker-compose.prod.yml down

docker-logs:
	docker-compose -f docker/docker-compose.yml logs -f

docker-shell:
	docker-compose -f docker/docker-compose.yml exec app bash

# Firebase setup
setup-firebase:
	python scripts/setup_firebase.py

# Database migrations (placeholder for future use)
migrate:
	@echo "Database migrations not implemented yet"

# Environment setup
setup-env:
	cp .env.example .env
	@echo "Please edit .env file with your configuration"

# Pre-commit hooks
install-hooks:
	pre-commit install

run-hooks:
	pre-commit run --all-files

# Documentation (placeholder for future use)
docs:
	@echo "Documentation generation not implemented yet"

# Monitoring
logs:
	tail -f logs/app.log

# Health check
health:
	curl -f http://localhost:8000/api/v1/health/live || exit 1

# Load testing (placeholder for future use)
load-test:
	@echo "Load testing not implemented yet"

# Backup (placeholder for future use)
backup:
	@echo "Backup functionality not implemented yet"

# Security scan (placeholder for future use)
security-scan:
	@echo "Security scanning not implemented yet"

# Performance profiling (placeholder for future use)
profile:
	@echo "Performance profiling not implemented yet"

# All-in-one development setup
setup: setup-env install setup-firebase
	@echo "Development environment setup complete!"
	@echo "Run 'make dev' to start the development server"

# All-in-one production deployment
deploy: clean format-check lint test docker-build-prod
	@echo "Production deployment ready!"
	@echo "Run 'make docker-run-prod' to start production containers"

# CI/CD pipeline simulation
ci: format-check lint test
	@echo "CI pipeline completed successfully!"
