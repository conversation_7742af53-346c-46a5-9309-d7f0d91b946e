"""
Firebase configuration and initialization.
"""

import json
import logging
from typing import Optional, Dict, Any

import firebase_admin
import requests
from firebase_admin import auth, credentials, firestore, storage
from google.cloud.firestore import Client as FirestoreClient

from app.config.settings import settings

logger = logging.getLogger(__name__)


class FirebaseConfig:
    """Firebase configuration and client management."""
    
    def __init__(self):
        self._app: Optional[firebase_admin.App] = None
        self._firestore_client: Optional[FirestoreClient] = None
        self._storage_bucket = None
        self._initialized = False
    
    def initialize(self) -> None:
        """Initialize Firebase app and services."""
        if self._initialized:
            logger.warning("Firebase already initialized")
            return
        
        try:
            # Initialize Firebase Admin SDK
            cred = credentials.Certificate(settings.firebase_credentials)
            self._app = firebase_admin.initialize_app(
                cred,
                {
                    "projectId": settings.firebase_project_id,
                    "storageBucket": f"{settings.firebase_project_id}.appspot.com",
                }
            )
            
            # Initialize Firestore client
            self._firestore_client = firestore.client(app=self._app)
            
            # Initialize Storage bucket
            self._storage_bucket = storage.bucket(app=self._app)
            
            self._initialized = True
            logger.info("Firebase initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Firebase: {e}")
            raise
    
    @property
    def app(self) -> firebase_admin.App:
        """Get Firebase app instance."""
        if not self._initialized:
            self.initialize()
        return self._app
    
    @property
    def firestore(self) -> FirestoreClient:
        """Get Firestore client."""
        if not self._initialized:
            self.initialize()
        return self._firestore_client
    
    @property
    def storage(self):
        """Get Storage bucket."""
        if not self._initialized:
            self.initialize()
        return self._storage_bucket
    
    def verify_id_token(self, id_token: str) -> dict:
        """Verify Firebase ID token."""
        try:
            decoded_token = auth.verify_id_token(id_token, app=self.app)
            return decoded_token
        except Exception as e:
            logger.error(f"Failed to verify ID token: {e}")
            raise
    
    def get_user(self, uid: str) -> auth.UserRecord:
        """Get user by UID."""
        try:
            user = auth.get_user(uid, app=self.app)
            return user
        except Exception as e:
            logger.error(f"Failed to get user {uid}: {e}")
            raise
    
    def create_user(self, email: str, password: str, **kwargs) -> auth.UserRecord:
        """Create a new user."""
        try:
            user = auth.create_user(
                email=email,
                password=password,
                app=self.app,
                **kwargs
            )
            logger.info(f"Created user: {user.uid}")
            return user
        except Exception as e:
            logger.error(f"Failed to create user: {e}")
            raise
    
    def update_user(self, uid: str, **kwargs) -> auth.UserRecord:
        """Update user information."""
        try:
            user = auth.update_user(uid, app=self.app, **kwargs)
            logger.info(f"Updated user: {uid}")
            return user
        except Exception as e:
            logger.error(f"Failed to update user {uid}: {e}")
            raise
    
    def delete_user(self, uid: str) -> None:
        """Delete a user."""
        try:
            auth.delete_user(uid, app=self.app)
            logger.info(f"Deleted user: {uid}")
        except Exception as e:
            logger.error(f"Failed to delete user {uid}: {e}")
            raise
    
    def set_custom_claims(self, uid: str, custom_claims: dict) -> None:
        """Set custom claims for a user."""
        try:
            auth.set_custom_user_claims(uid, custom_claims, app=self.app)
            logger.info(f"Set custom claims for user: {uid}")
        except Exception as e:
            logger.error(f"Failed to set custom claims for user {uid}: {e}")
            raise
    
    def create_custom_token(self, uid: str, additional_claims: Optional[dict] = None) -> str:
        """Create a custom token for a user."""
        try:
            if additional_claims:
                token = auth.create_custom_token(uid, additional_claims, app=self.app)
            else:
                token = auth.create_custom_token(uid, app=self.app)
            return token.decode('utf-8')
        except Exception as e:
            logger.error(f"Failed to create custom token for user {uid}: {e}")
            raise
    
    def authenticate_user(self, email: str, password: str) -> Dict[str, Any]:
        """Authenticate user with email and password using Firebase Auth REST API."""
        try:
            # Firebase Auth REST API endpoint for sign in
            url = f"https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key={settings.firebase_web_api_key}"

            payload = {
                "email": email,
                "password": password,
                "returnSecureToken": True
            }

            response = requests.post(url, json=payload)

            if response.status_code == 200:
                data = response.json()
                logger.info(f"User authenticated successfully: {email}")
                return data
            else:
                error_data = response.json()
                error_message = error_data.get("error", {}).get("message", "Authentication failed")
                logger.warning(f"Authentication failed for {email}: {error_message}")
                raise Exception(error_message)

        except Exception as e:
            logger.error(f"Failed to authenticate user {email}: {e}")
            raise

    def send_email_verification(self, id_token: str) -> bool:
        """Send email verification using Firebase Auth REST API."""
        try:
            url = f"https://identitytoolkit.googleapis.com/v1/accounts:sendOobCode?key={settings.firebase_web_api_key}"

            payload = {
                "requestType": "VERIFY_EMAIL",
                "idToken": id_token
            }

            response = requests.post(url, json=payload)

            if response.status_code == 200:
                logger.info("Email verification sent successfully")
                return True
            else:
                error_data = response.json()
                error_message = error_data.get("error", {}).get("message", "Failed to send verification")
                logger.error(f"Failed to send email verification: {error_message}")
                return False

        except Exception as e:
            logger.error(f"Failed to send email verification: {e}")
            return False

    def send_password_reset_email(self, email: str) -> bool:
        """Send password reset email using Firebase Auth REST API."""
        try:
            url = f"https://identitytoolkit.googleapis.com/v1/accounts:sendOobCode?key={settings.firebase_web_api_key}"

            payload = {
                "requestType": "PASSWORD_RESET",
                "email": email
            }

            response = requests.post(url, json=payload)

            if response.status_code == 200:
                logger.info(f"Password reset email sent to: {email}")
                return True
            else:
                error_data = response.json()
                error_message = error_data.get("error", {}).get("message", "Failed to send reset email")
                logger.warning(f"Failed to send password reset email to {email}: {error_message}")
                return False

        except Exception as e:
            logger.error(f"Failed to send password reset email to {email}: {e}")
            return False

    def health_check(self) -> bool:
        """Check Firebase connection health."""
        try:
            # Try to access Firestore
            self.firestore.collection("health_check").limit(1).get()
            return True
        except Exception as e:
            logger.error(f"Firebase health check failed: {e}")
            return False


# Global Firebase instance
firebase_config = FirebaseConfig()
