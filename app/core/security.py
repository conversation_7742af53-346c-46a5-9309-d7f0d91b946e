"""
Security utilities and authentication helpers.
"""

import hashlib
import secrets
from datetime import datetime, timedelta
from typing import Any, Dict, Optional, Union

from fastapi import Depends, HTTPException, Request, status
from fastapi.security import HTT<PERSON>uthorizationCredentials, HTTPBearer
from jose import JW<PERSON>rror, jwt
from passlib.context import CryptContext

from app.config.firebase import firebase_config
from app.config.settings import settings
from app.core.exceptions import AuthenticationError, AuthorizationError
from app.models.user import User, UserRole
from app.utils.logger import get_logger

logger = get_logger(__name__)

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# HTTP Bearer token scheme
security = HTTPBearer(auto_error=False)


def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """Create JWT access token."""
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.access_token_expire_minutes)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.secret_key, algorithm=settings.algorithm)
    
    return encoded_jwt


def verify_token(token: str) -> Dict[str, Any]:
    """Verify JWT token."""
    try:
        payload = jwt.decode(token, settings.secret_key, algorithms=[settings.algorithm])
        return payload
    except JWTError as e:
        logger.warning(f"Token verification failed: {e}")
        raise AuthenticationError("Invalid token")


def hash_password(password: str) -> str:
    """Hash password using bcrypt."""
    return pwd_context.hash(password)


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify password against hash."""
    return pwd_context.verify(plain_password, hashed_password)


def generate_api_key() -> str:
    """Generate a secure API key."""
    return secrets.token_urlsafe(32)


def hash_api_key(api_key: str) -> str:
    """Hash API key for storage."""
    return hashlib.sha256(api_key.encode()).hexdigest()


def verify_api_key(api_key: str, hashed_key: str) -> bool:
    """Verify API key against hash."""
    return hash_api_key(api_key) == hashed_key


async def get_current_user_from_token(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> User:
    """Get current user from JWT token."""
    if not credentials:
        raise AuthenticationError("Authentication required")
    
    try:
        # Verify JWT token
        payload = verify_token(credentials.credentials)
        user_id = payload.get("sub")
        
        if not user_id:
            raise AuthenticationError("Invalid token payload")
        
        # Get user from Firebase
        user_record = firebase_config.get_user(user_id)
        
        # Convert to User model
        user = User(
            uid=user_record.uid,
            email=user_record.email,
            display_name=user_record.display_name,
            role=UserRole.USER,  # Default role, should be fetched from custom claims
            created_at=datetime.fromtimestamp(user_record.user_metadata.creation_timestamp / 1000),
            updated_at=datetime.fromtimestamp(user_record.user_metadata.last_sign_in_timestamp / 1000) if user_record.user_metadata.last_sign_in_timestamp else datetime.utcnow(),
            email_verified=user_record.email_verified,
            phone_number=user_record.phone_number,
            photo_url=user_record.photo_url,
            custom_claims=user_record.custom_claims or {},
        )
        
        # Extract role from custom claims
        if user_record.custom_claims and "role" in user_record.custom_claims:
            user.role = UserRole(user_record.custom_claims["role"])
        
        return user
        
    except Exception as e:
        logger.error(f"Authentication failed: {e}")
        raise AuthenticationError("Authentication failed")


async def get_current_user_from_firebase_token(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> User:
    """Get current user from Firebase ID token."""
    if not credentials:
        raise AuthenticationError("Authentication required")
    
    try:
        # Verify Firebase ID token
        decoded_token = firebase_config.verify_id_token(credentials.credentials)
        user_id = decoded_token["uid"]
        
        # Get user from Firebase
        user_record = firebase_config.get_user(user_id)
        
        # Convert to User model
        user = User(
            uid=user_record.uid,
            email=user_record.email,
            display_name=user_record.display_name,
            role=UserRole.USER,
            created_at=datetime.fromtimestamp(user_record.user_metadata.creation_timestamp / 1000),
            updated_at=datetime.fromtimestamp(user_record.user_metadata.last_sign_in_timestamp / 1000) if user_record.user_metadata.last_sign_in_timestamp else datetime.utcnow(),
            email_verified=user_record.email_verified,
            phone_number=user_record.phone_number,
            photo_url=user_record.photo_url,
            custom_claims=user_record.custom_claims or {},
        )
        
        # Extract role from custom claims
        if user_record.custom_claims and "role" in user_record.custom_claims:
            user.role = UserRole(user_record.custom_claims["role"])
        
        return user
        
    except Exception as e:
        logger.error(f"Firebase authentication failed: {e}")
        raise AuthenticationError("Authentication failed")


async def get_current_user_from_api_key(request: Request) -> User:
    """Get current user from API key."""
    api_key = request.headers.get("X-API-Key")
    
    if not api_key:
        raise AuthenticationError("API key required")
    
    try:
        # TODO: Implement API key validation against database
        # This would involve:
        # 1. Hash the provided API key
        # 2. Look up the hashed key in the database
        # 3. Check if the key is active and not expired
        # 4. Get the associated user
        
        # For now, return a placeholder
        raise AuthenticationError("API key authentication not implemented")
        
    except Exception as e:
        logger.error(f"API key authentication failed: {e}")
        raise AuthenticationError("Invalid API key")


def require_role(required_role: UserRole):
    """Dependency to require specific user role."""
    def role_checker(current_user: User = Depends(get_current_user_from_firebase_token)):
        if current_user.role != required_role:
            raise AuthorizationError(f"Role {required_role.value} required")
        return current_user
    
    return role_checker


def require_roles(required_roles: list[UserRole]):
    """Dependency to require one of multiple user roles."""
    def roles_checker(current_user: User = Depends(get_current_user_from_firebase_token)):
        if current_user.role not in required_roles:
            role_names = [role.value for role in required_roles]
            raise AuthorizationError(f"One of roles {role_names} required")
        return current_user
    
    return roles_checker


def require_admin(current_user: User = Depends(get_current_user_from_firebase_token)):
    """Dependency to require admin role."""
    if current_user.role != UserRole.ADMIN:
        raise AuthorizationError("Admin role required")
    return current_user


def require_active_user(current_user: User = Depends(get_current_user_from_firebase_token)):
    """Dependency to require active user."""
    if current_user.status != "active":
        raise AuthorizationError("Active user account required")
    return current_user


class RateLimiter:
    """Simple rate limiter for API endpoints."""
    
    def __init__(self, max_requests: int, window_seconds: int):
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests = {}
    
    def is_allowed(self, key: str) -> bool:
        """Check if request is allowed."""
        import time
        
        current_time = time.time()
        
        # Clean old requests
        if key in self.requests:
            self.requests[key] = [
                req_time for req_time in self.requests[key]
                if current_time - req_time < self.window_seconds
            ]
        else:
            self.requests[key] = []
        
        # Check rate limit
        if len(self.requests[key]) >= self.max_requests:
            return False
        
        # Add current request
        self.requests[key].append(current_time)
        return True


# Global rate limiter instances
auth_rate_limiter = RateLimiter(max_requests=5, window_seconds=60)  # 5 auth attempts per minute
api_rate_limiter = RateLimiter(max_requests=100, window_seconds=60)  # 100 API calls per minute
