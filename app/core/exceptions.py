"""
Custom exceptions for the application.
"""

from datetime import datetime
from typing import Any, Dict, Optional

from fastapi import HTT<PERSON>Ex<PERSON>, status


class CustomHTTPException(HTTPException):
    """Base custom HTTP exception with additional context."""
    
    def __init__(
        self,
        status_code: int,
        detail: str,
        error_code: str,
        headers: Optional[Dict[str, Any]] = None,
        context: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(status_code=status_code, detail=detail, headers=headers)
        self.error_code = error_code
        self.context = context or {}
        self.timestamp = datetime.utcnow()


class AuthenticationError(CustomHTTPException):
    """Authentication related errors."""
    
    def __init__(self, detail: str = "Authentication failed", context: Optional[Dict[str, Any]] = None):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=detail,
            error_code="AUTHENTICATION_ERROR",
            headers={"WWW-Authenticate": "Bearer"},
            context=context,
        )


class AuthorizationError(CustomHTTPException):
    """Authorization related errors."""
    
    def __init__(self, detail: str = "Insufficient permissions", context: Optional[Dict[str, Any]] = None):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=detail,
            error_code="AUTHORIZATION_ERROR",
            context=context,
        )


class ValidationError(CustomHTTPException):
    """Data validation errors."""
    
    def __init__(self, detail: str = "Validation failed", context: Optional[Dict[str, Any]] = None):
        super().__init__(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=detail,
            error_code="VALIDATION_ERROR",
            context=context,
        )


class NotFoundError(CustomHTTPException):
    """Resource not found errors."""
    
    def __init__(self, detail: str = "Resource not found", context: Optional[Dict[str, Any]] = None):
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=detail,
            error_code="NOT_FOUND_ERROR",
            context=context,
        )


class ConflictError(CustomHTTPException):
    """Resource conflict errors."""
    
    def __init__(self, detail: str = "Resource conflict", context: Optional[Dict[str, Any]] = None):
        super().__init__(
            status_code=status.HTTP_409_CONFLICT,
            detail=detail,
            error_code="CONFLICT_ERROR",
            context=context,
        )


class RateLimitError(CustomHTTPException):
    """Rate limiting errors."""
    
    def __init__(self, detail: str = "Rate limit exceeded", context: Optional[Dict[str, Any]] = None):
        super().__init__(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=detail,
            error_code="RATE_LIMIT_ERROR",
            context=context,
        )


class ExternalServiceError(CustomHTTPException):
    """External service errors."""
    
    def __init__(self, detail: str = "External service error", context: Optional[Dict[str, Any]] = None):
        super().__init__(
            status_code=status.HTTP_502_BAD_GATEWAY,
            detail=detail,
            error_code="EXTERNAL_SERVICE_ERROR",
            context=context,
        )


class ScrapingError(CustomHTTPException):
    """Web scraping related errors."""
    
    def __init__(self, detail: str = "Scraping failed", context: Optional[Dict[str, Any]] = None):
        super().__init__(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=detail,
            error_code="SCRAPING_ERROR",
            context=context,
        )


class DataProcessingError(CustomHTTPException):
    """Data processing errors."""
    
    def __init__(self, detail: str = "Data processing failed", context: Optional[Dict[str, Any]] = None):
        super().__init__(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=detail,
            error_code="DATA_PROCESSING_ERROR",
            context=context,
        )


class FirebaseError(CustomHTTPException):
    """Firebase related errors."""
    
    def __init__(self, detail: str = "Firebase operation failed", context: Optional[Dict[str, Any]] = None):
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=detail,
            error_code="FIREBASE_ERROR",
            context=context,
        )


class TaskQueueError(CustomHTTPException):
    """Task queue related errors."""
    
    def __init__(self, detail: str = "Task queue operation failed", context: Optional[Dict[str, Any]] = None):
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=detail,
            error_code="TASK_QUEUE_ERROR",
            context=context,
        )


class ConfigurationError(Exception):
    """Configuration related errors."""
    pass


class DatabaseError(Exception):
    """Database related errors."""
    pass
