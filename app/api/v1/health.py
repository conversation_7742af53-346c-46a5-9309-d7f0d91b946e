"""
Health check endpoints.
"""

import time
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel

from app.config.firebase import firebase_config
from app.config.settings import settings
from app.services.scraper import WebScrapingService
from app.utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter()


class HealthStatus(BaseModel):
    """Health status response model."""
    status: str
    timestamp: datetime
    version: str
    environment: str
    uptime_seconds: float
    checks: Dict[str, Any]


class ServiceCheck(BaseModel):
    """Individual service check result."""
    status: str  # "healthy", "unhealthy", "unknown"
    response_time_ms: float
    message: str
    details: Dict[str, Any] = {}


# Application start time for uptime calculation
app_start_time = time.time()


@router.get("/", response_model=HealthStatus)
async def health_check():
    """
    Comprehensive health check endpoint.
    
    Returns the overall health status of the application and its dependencies.
    """
    start_time = time.time()
    checks = {}
    overall_status = "healthy"
    
    # Check Firebase connection
    firebase_check = await check_firebase()
    checks["firebase"] = firebase_check
    if firebase_check.status != "healthy":
        overall_status = "unhealthy"
    
    # Check web scraping service
    scraper_check = await check_scraper_service()
    checks["scraper"] = scraper_check
    if scraper_check.status != "healthy":
        overall_status = "degraded" if overall_status == "healthy" else "unhealthy"
    
    # Check system resources
    system_check = check_system_resources()
    checks["system"] = system_check
    if system_check.status != "healthy":
        overall_status = "degraded" if overall_status == "healthy" else "unhealthy"
    
    # Calculate uptime
    uptime = time.time() - app_start_time
    
    return HealthStatus(
        status=overall_status,
        timestamp=datetime.utcnow(),
        version=settings.app_version,
        environment=settings.environment,
        uptime_seconds=round(uptime, 2),
        checks=checks,
    )


@router.get("/live")
async def liveness_probe():
    """
    Kubernetes liveness probe endpoint.
    
    Returns 200 if the application is running.
    """
    return {"status": "alive", "timestamp": datetime.utcnow()}


@router.get("/ready")
async def readiness_probe():
    """
    Kubernetes readiness probe endpoint.
    
    Returns 200 if the application is ready to serve traffic.
    """
    # Check critical dependencies
    try:
        # Quick Firebase check
        firebase_healthy = firebase_config.health_check()
        
        if not firebase_healthy:
            raise HTTPException(
                status_code=503,
                detail="Service not ready - Firebase connection failed"
            )
        
        return {
            "status": "ready",
            "timestamp": datetime.utcnow(),
            "checks": {
                "firebase": "healthy",
            }
        }
        
    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        raise HTTPException(
            status_code=503,
            detail=f"Service not ready: {str(e)}"
        )


@router.get("/firebase")
async def firebase_health():
    """
    Detailed Firebase health check.
    """
    check_result = await check_firebase()
    
    if check_result.status == "healthy":
        return check_result
    else:
        raise HTTPException(
            status_code=503,
            detail=f"Firebase unhealthy: {check_result.message}"
        )


@router.get("/scraper")
async def scraper_health():
    """
    Detailed scraper service health check.
    """
    check_result = await check_scraper_service()
    
    if check_result.status == "healthy":
        return check_result
    else:
        raise HTTPException(
            status_code=503,
            detail=f"Scraper service unhealthy: {check_result.message}"
        )


async def check_firebase() -> ServiceCheck:
    """Check Firebase service health."""
    start_time = time.time()
    
    try:
        # Test Firebase connection
        is_healthy = firebase_config.health_check()
        response_time = (time.time() - start_time) * 1000
        
        if is_healthy:
            return ServiceCheck(
                status="healthy",
                response_time_ms=round(response_time, 2),
                message="Firebase connection successful",
                details={
                    "project_id": settings.firebase_project_id,
                    "services": ["auth", "firestore", "storage"],
                }
            )
        else:
            return ServiceCheck(
                status="unhealthy",
                response_time_ms=round(response_time, 2),
                message="Firebase connection failed",
                details={"project_id": settings.firebase_project_id}
            )
            
    except Exception as e:
        response_time = (time.time() - start_time) * 1000
        logger.error(f"Firebase health check error: {e}")
        
        return ServiceCheck(
            status="unhealthy",
            response_time_ms=round(response_time, 2),
            message=f"Firebase check failed: {str(e)}",
            details={"error": str(e)}
        )


async def check_scraper_service() -> ServiceCheck:
    """Check web scraper service health."""
    start_time = time.time()
    
    try:
        scraper = WebScrapingService()
        is_healthy = await scraper.health_check()
        response_time = (time.time() - start_time) * 1000

        if is_healthy:
            return ServiceCheck(
                status="healthy",
                response_time_ms=round(response_time, 2),
                message="Scraper service operational",
                details={
                    "max_concurrent_requests": settings.crawl4ai_max_concurrent_requests,
                    "request_timeout": settings.crawl4ai_request_timeout,
                }
            )
        else:
            return ServiceCheck(
                status="unhealthy",
                response_time_ms=round(response_time, 2),
                message="Scraper service check failed",
                details={}
            )
                
    except Exception as e:
        response_time = (time.time() - start_time) * 1000
        logger.error(f"Scraper health check error: {e}")
        
        return ServiceCheck(
            status="unhealthy",
            response_time_ms=round(response_time, 2),
            message=f"Scraper check failed: {str(e)}",
            details={"error": str(e)}
        )


def check_system_resources() -> ServiceCheck:
    """Check system resource availability."""
    start_time = time.time()
    
    try:
        import psutil
        
        # Get system metrics
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # Determine health based on resource usage
        status = "healthy"
        warnings = []
        
        if cpu_percent > 90:
            status = "unhealthy"
            warnings.append(f"High CPU usage: {cpu_percent}%")
        elif cpu_percent > 70:
            status = "degraded"
            warnings.append(f"Elevated CPU usage: {cpu_percent}%")
        
        if memory.percent > 90:
            status = "unhealthy"
            warnings.append(f"High memory usage: {memory.percent}%")
        elif memory.percent > 70:
            status = "degraded"
            warnings.append(f"Elevated memory usage: {memory.percent}%")
        
        if disk.percent > 90:
            status = "unhealthy"
            warnings.append(f"High disk usage: {disk.percent}%")
        elif disk.percent > 80:
            status = "degraded"
            warnings.append(f"Elevated disk usage: {disk.percent}%")
        
        response_time = (time.time() - start_time) * 1000
        
        return ServiceCheck(
            status=status,
            response_time_ms=round(response_time, 2),
            message="System resources checked" + (f" - {', '.join(warnings)}" if warnings else ""),
            details={
                "cpu_percent": cpu_percent,
                "memory_percent": memory.percent,
                "memory_available_gb": round(memory.available / (1024**3), 2),
                "disk_percent": disk.percent,
                "disk_free_gb": round(disk.free / (1024**3), 2),
            }
        )
        
    except ImportError:
        # psutil not available
        response_time = (time.time() - start_time) * 1000
        return ServiceCheck(
            status="unknown",
            response_time_ms=round(response_time, 2),
            message="System monitoring not available (psutil not installed)",
            details={}
        )
        
    except Exception as e:
        response_time = (time.time() - start_time) * 1000
        logger.error(f"System resource check error: {e}")
        
        return ServiceCheck(
            status="unknown",
            response_time_ms=round(response_time, 2),
            message=f"System check failed: {str(e)}",
            details={"error": str(e)}
        )
