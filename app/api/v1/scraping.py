"""
Web scraping endpoints.
"""

import uuid
from datetime import datetime
from typing import List, Optional

from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException, Query, status
from pydantic import BaseModel, HttpUrl

from app.core.security import get_current_user_from_token
from app.models.scraping_job import (
    JobStatus,
    ScrapingJob,
    ScrapingJobCreate,
    ScrapingJobFilter,
    ScrapingJobSummary,
    ScrapingJobUpdate,
)
from app.models.user import User
from app.services.data_processor import DataCleaningConfig, DataProcessingService, DataTransformConfig
from app.services.scraper import ExtractionConfig, ScrapingConfig, WebScrapingService
from app.utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter()


class QuickScrapeRequest(BaseModel):
    """Quick scrape request for single URL."""
    url: HttpUrl
    extract_text: bool = True
    extract_links: bool = False
    extract_images: bool = False
    javascript: bool = True
    clean_data: bool = True


class QuickScrapeResponse(BaseModel):
    """Quick scrape response."""
    url: str
    title: Optional[str] = None
    content: Optional[str] = None
    links: Optional[List[str]] = None
    images: Optional[List[str]] = None
    metadata: dict = {}
    processing_time: float
    timestamp: datetime


class JobCreateResponse(BaseModel):
    """Job creation response."""
    job_id: str
    message: str
    status: JobStatus
    estimated_completion: Optional[datetime] = None


class JobListResponse(BaseModel):
    """Job list response."""
    jobs: List[ScrapingJobSummary]
    total: int
    page: int
    page_size: int
    has_next: bool


@router.post("/quick", response_model=QuickScrapeResponse)
async def quick_scrape(
    request: QuickScrapeRequest,
    current_user: User = Depends(get_current_user_from_token),
):
    """
    Quick scrape a single URL without creating a job.
    
    This endpoint is for immediate scraping of a single URL with basic options.
    For more complex scraping tasks, use the job-based endpoints.
    """
    try:
        logger.info(f"Quick scrape requested by user {current_user.uid} for URL: {request.url}")
        
        # Create scraping configuration
        scraping_config = ScrapingConfig(
            url=str(request.url),
            javascript=request.javascript,
            screenshot=False,
            pdf=False,
        )
        
        # For now, use basic scraping without complex extraction strategies
        extraction_config = None
        
        # Perform scraping
        async with WebScrapingService() as scraper:
            result = await scraper.scrape_url(scraping_config, extraction_config)
        
        if result.error:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail=f"Scraping failed: {result.error}"
            )
        
        # Clean data if requested
        content = result.content
        if request.clean_data and content:
            data_processor = DataProcessingService()
            cleaning_config = DataCleaningConfig(
                remove_html=True,
                normalize_whitespace=True,
                remove_empty_fields=True,
            )
            
            cleaned_data = data_processor.clean_data({"content": content}, cleaning_config)
            content = cleaned_data.get("content", content)
        
        response = QuickScrapeResponse(
            url=str(request.url),
            title=result.title,
            content=content,
            links=result.links if request.extract_links else None,
            images=result.images if request.extract_images else None,
            metadata=result.metadata or {},
            processing_time=result.duration,
            timestamp=datetime.fromtimestamp(result.timestamp),
        )
        
        logger.info(f"Quick scrape completed for user {current_user.uid}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Quick scrape failed for user {current_user.uid}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Scraping operation failed"
        )


@router.post("/jobs", response_model=JobCreateResponse)
async def create_scraping_job(
    job_data: ScrapingJobCreate,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user_from_token),
):
    """
    Create a new scraping job.
    
    This endpoint creates a scraping job that will be processed in the background.
    Use this for complex scraping tasks with multiple URLs or advanced configurations.
    """
    try:
        logger.info(f"Scraping job creation requested by user {current_user.uid}")
        
        # Generate job ID
        job_id = str(uuid.uuid4())
        
        # Create job object
        job = ScrapingJob(
            job_id=job_id,
            user_id=current_user.uid,
            name=job_data.name,
            description=job_data.description,
            config=job_data.config,
            status=JobStatus.PENDING,
            priority=job_data.priority,
            scheduled_at=job_data.scheduled_at,
            tags=job_data.tags,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )
        
        # Execute immediately in background
        background_tasks.add_task(execute_scraping_job, job)
        logger.info(f"Job {job_id} queued for immediate execution")

        return JobCreateResponse(
            job_id=job_id,
            message="Scraping job created successfully",
            status=job.status,
            estimated_completion=None,
        )
        
    except Exception as e:
        logger.error(f"Job creation failed for user {current_user.uid}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create scraping job"
        )











async def execute_scraping_job(job: ScrapingJob):
    """
    Execute a scraping job in the background.
    
    This function would typically be executed by a task queue like Celery.
    """
    try:
        logger.info(f"Starting execution of job {job.job_id}")
        
        # Update job status
        job.status = JobStatus.RUNNING
        job.started_at = datetime.utcnow()
        job.updated_at = datetime.utcnow()
        
        # Job status updates would be saved to database in production
        
        # Create scraping service
        async with WebScrapingService() as scraper:
            # Convert job config to scraping configs
            urls = [str(url) for url in job.config.urls]
            
            scraping_config = ScrapingConfig(
                url=urls[0],  # Will be overridden for each URL
                max_depth=job.config.max_depth,
                delay=job.config.delay,
                timeout=job.config.timeout,
                user_agent=job.config.user_agent,
                headers=job.config.headers,
                cookies=job.config.cookies,
                proxy=job.config.proxy,
                javascript=job.config.javascript,
                wait_for=job.config.wait_for,
                screenshot=job.config.screenshot,
                pdf=job.config.pdf,
            )
            
            extraction_config = ExtractionConfig(
                strategy=job.config.extraction_strategy,
                selectors=job.config.css_selectors,
                schema=job.config.json_schema,
                llm_prompt=job.config.llm_prompt,
                similarity_threshold=job.config.similarity_threshold,
            )
            
            # Execute scraping
            results = await scraper.scrape_multiple_urls(
                urls=urls,
                config=scraping_config,
                extraction_config=extraction_config,
                max_concurrent=5,
            )
            
            # Process results
            if job.config.clean_data:
                data_processor = DataProcessingService()
                cleaning_config = DataCleaningConfig(
                    remove_html=job.config.remove_html,
                    normalize_whitespace=job.config.normalize_whitespace,
                    min_text_length=job.config.min_text_length,
                    max_text_length=job.config.max_text_length,
                )
                
                # Clean each result
                for result in results:
                    if result.content:
                        cleaned_data = data_processor.clean_data(
                            {"content": result.content}, 
                            cleaning_config
                        )
                        result.content = cleaned_data.get("content", result.content)
            
            # Update job with results
            job.results = results
            job.stats.total_urls = len(urls)
            job.stats.processed_urls = len(results)
            job.stats.successful_urls = len([r for r in results if not r.error])
            job.stats.failed_urls = len([r for r in results if r.error])
            job.status = JobStatus.COMPLETED
            job.completed_at = datetime.utcnow()
            job.updated_at = datetime.utcnow()
            
            # Results would be saved to database and file storage in production
            
            logger.info(f"Job {job.job_id} completed successfully")
    
    except Exception as e:
        logger.error(f"Job {job.job_id} failed: {e}")
        
        # Update job with error
        job.status = JobStatus.FAILED
        job.error_message = str(e)
        job.completed_at = datetime.utcnow()
        job.updated_at = datetime.utcnow()
        
        # Error status would be saved to database in production
