"""
Authentication endpoints.
"""

from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, Optional

from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, Request, status
from fastapi.security import HTTPAuthorizationCredentials
from pydantic import BaseModel, EmailStr

from app.config.firebase import firebase_config
from app.config.settings import settings
from app.core.exceptions import AuthenticationError, ValidationError
from app.core.security import (
    auth_rate_limiter,
    create_access_token,
    get_current_user_from_firebase_token,
    get_current_user_from_token,
    hash_password,
    security,
    verify_password,
)
from app.models.user import User, UserCreate, UserProfile, UserRole
from app.utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter()


class LoginRequest(BaseModel):
    """Login request model."""
    email: EmailStr
    password: str


class LoginResponse(BaseModel):
    """Login response model."""
    access_token: str
    token_type: str
    expires_in: int
    user: UserProfile


class RegisterRequest(BaseModel):
    """Registration request model."""
    email: EmailStr
    password: str
    display_name: str


class RegisterResponse(BaseModel):
    """Registration response model."""
    message: str
    user_id: str
    email_verification_sent: bool


class TokenRefreshRequest(BaseModel):
    """Token refresh request model."""
    refresh_token: str


class PasswordResetRequest(BaseModel):
    """Password reset request model."""
    email: EmailStr


class PasswordChangeRequest(BaseModel):
    """Password change request model."""
    current_password: str
    new_password: str


class PasswordChangeRequest(BaseModel):
    """Password change request model."""
    current_password: str
    new_password: str


@router.post("/register", response_model=RegisterResponse)
async def register(request: Request, user_data: RegisterRequest):
    """
    Register a new user account.
    
    Creates a new user in Firebase Auth and sets up initial user profile.
    """
    client_ip = request.client.host if request.client else "unknown"
    
    # Rate limiting
    if not auth_rate_limiter.is_allowed(client_ip):
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Too many registration attempts. Please try again later."
        )
    
    try:
        logger.info(f"Registration attempt for email: {user_data.email}")
        
        # Create user in Firebase Auth
        user_record = firebase_config.create_user(
            email=user_data.email,
            password=user_data.password,
            display_name=user_data.display_name,
            email_verified=False,
        )
        
        # Set default custom claims
        firebase_config.set_custom_claims(user_record.uid, {
            "role": UserRole.USER.value,
            "status": "active",
            "created_at": datetime.utcnow().isoformat(),
        })
        
        # Create a temporary ID token to send email verification
        try:
            # Create custom token and exchange for ID token
            custom_token = firebase_config.create_custom_token(user_record.uid)

            # Exchange custom token for ID token using Firebase Auth REST API
            url = f"https://identitytoolkit.googleapis.com/v1/accounts:signInWithCustomToken?key={settings.firebase_web_api_key}"
            payload = {
                "token": custom_token,
                "returnSecureToken": True
            }

            import requests
            response = requests.post(url, json=payload)

            if response.status_code == 200:
                token_data = response.json()
                id_token = token_data.get("idToken")

                # Send email verification
                email_sent = firebase_config.send_email_verification(id_token)

                logger.info(f"User registered successfully: {user_record.uid}, email verification sent: {email_sent}")

                return RegisterResponse(
                    message="User registered successfully. Please check your email for verification.",
                    user_id=user_record.uid,
                    email_verification_sent=email_sent,
                )
            else:
                logger.warning(f"Failed to get ID token for email verification: {response.text}")
                return RegisterResponse(
                    message="User registered successfully, but email verification could not be sent.",
                    user_id=user_record.uid,
                    email_verification_sent=False,
                )

        except Exception as email_error:
            logger.error(f"Failed to send email verification: {email_error}")
            return RegisterResponse(
                message="User registered successfully, but email verification could not be sent.",
                user_id=user_record.uid,
                email_verification_sent=False,
            )
        
    except Exception as e:
        logger.error(f"Registration failed for {user_data.email}: {e}")
        
        if "EMAIL_EXISTS" in str(e):
            raise ValidationError("Email address is already registered")
        elif "WEAK_PASSWORD" in str(e):
            raise ValidationError("Password is too weak")
        else:
            raise AuthenticationError(f"Registration failed: {str(e)}")


@router.post("/login", response_model=LoginResponse)
async def login(request: Request, login_data: LoginRequest):
    """
    Authenticate user and return access token.
    
    Note: This endpoint is for demonstration. In production, you would
    typically use Firebase Auth SDK on the client side for authentication.
    """
    client_ip = request.client.host if request.client else "unknown"
    
    # Rate limiting
    if not auth_rate_limiter.is_allowed(client_ip):
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Too many login attempts. Please try again later."
        )
    
    try:
        logger.info(f"Login attempt for email: {login_data.email}")

        # Authenticate user using Firebase Auth REST API
        try:
            auth_data = firebase_config.authenticate_user(login_data.email, login_data.password)
            id_token = auth_data.get("idToken")
            user_id = auth_data.get("localId")

            if not id_token or not user_id:
                raise AuthenticationError("Invalid authentication response")

        except Exception as e:
            error_msg = str(e)
            if "INVALID_PASSWORD" in error_msg or "EMAIL_NOT_FOUND" in error_msg:
                raise AuthenticationError("Invalid email or password")
            elif "USER_DISABLED" in error_msg:
                raise AuthenticationError("User account has been disabled")
            elif "TOO_MANY_ATTEMPTS_TRY_LATER" in error_msg:
                raise AuthenticationError("Too many failed login attempts. Please try again later.")
            else:
                raise AuthenticationError("Login failed")

        # Get user record from Firebase
        user_record = firebase_config.get_user(user_id)
        
        # Create our own access token for API access
        access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
        access_token = create_access_token(
            data={"sub": user_record.uid, "email": user_record.email},
            expires_delta=access_token_expires,
        )
        
        # Create user profile
        user_profile = UserProfile(
            uid=user_record.uid,
            email=user_record.email,
            display_name=user_record.display_name,
            role=UserRole.USER,  # Should be fetched from custom claims
            status="active",
            created_at=datetime.fromtimestamp(user_record.user_metadata.creation_timestamp / 1000),
            email_verified=user_record.email_verified,
            photo_url=user_record.photo_url,
        )
        
        # Extract role from custom claims
        if user_record.custom_claims and "role" in user_record.custom_claims:
            user_profile.role = UserRole(user_record.custom_claims["role"])
        
        logger.info(f"User logged in successfully: {user_record.uid}")
        
        return LoginResponse(
            access_token=access_token,
            token_type="bearer",
            expires_in=settings.access_token_expire_minutes * 60,
            user=user_profile,
        )
        
    except AuthenticationError:
        raise
    except Exception as e:
        logger.error(f"Login failed for {login_data.email}: {e}")
        raise AuthenticationError("Login failed")


@router.post("/logout")
async def logout(current_user: User = Depends(get_current_user_from_token)):
    """
    Logout user.

    Note: JWT tokens are stateless, so logout is handled client-side by discarding the token.
    """
    try:
        logger.info(f"User logged out: {current_user.uid}")
        return {"message": "Logged out successfully"}

    except Exception as e:
        logger.error(f"Logout failed for user {current_user.uid}: {e}")
        raise AuthenticationError("Logout failed")


@router.get("/me", response_model=UserProfile)
async def get_current_user(current_user: User = Depends(get_current_user_from_token)):
    """
    Get current user profile.
    """
    try:
        user_profile = UserProfile(
            uid=current_user.uid,
            email=current_user.email,
            display_name=current_user.display_name,
            role=current_user.role,
            status=current_user.status,
            created_at=current_user.created_at,
            last_login=current_user.last_login,
            email_verified=current_user.email_verified,
            photo_url=current_user.photo_url,
        )
        
        return user_profile
        
    except Exception as e:
        logger.error(f"Failed to get user profile for {current_user.uid}: {e}")
        raise AuthenticationError("Failed to get user profile")


@router.post("/refresh")
async def refresh_token(refresh_data: TokenRefreshRequest):
    """
    Refresh access token using refresh token.
    
    In Firebase, this would be handled by the client SDK.
    """
    try:
        # TODO: Implement token refresh logic
        # This would involve verifying the refresh token and issuing a new access token
        
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="Token refresh not implemented"
        )
        
    except Exception as e:
        logger.error(f"Token refresh failed: {e}")
        raise AuthenticationError("Token refresh failed")


@router.post("/forgot-password")
async def forgot_password(request: Request, reset_data: PasswordResetRequest):
    """
    Send password reset email.
    """
    client_ip = request.client.host if request.client else "unknown"
    
    # Rate limiting
    if not auth_rate_limiter.is_allowed(client_ip):
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Too many password reset attempts. Please try again later."
        )
    
    try:
        logger.info(f"Password reset requested for email: {reset_data.email}")

        # Send password reset email using Firebase Auth REST API
        firebase_config.send_password_reset_email(reset_data.email)

        # Always return success to prevent email enumeration
        return {"message": "If the email exists, a password reset link has been sent"}

    except Exception as e:
        logger.error(f"Password reset failed for {reset_data.email}: {e}")
        # Still return success to prevent email enumeration
        return {"message": "If the email exists, a password reset link has been sent"}


@router.post("/change-password")
async def change_password(
    password_data: PasswordChangeRequest,
    current_user: User = Depends(get_current_user_from_token)
):
    """
    Change user password.

    Note: This requires the user to provide their current password for verification.
    """
    try:
        logger.info(f"Password change requested for user: {current_user.uid}")

        # Verify current password by attempting to authenticate
        try:
            firebase_config.authenticate_user(current_user.email, password_data.current_password)
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Current password is incorrect"
            )

        # Password change would be implemented using Firebase Admin SDK
        # For now, return a message indicating the feature needs Firebase Admin SDK setup
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="Password change requires Firebase Admin SDK configuration"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Password change failed for user {current_user.uid}: {e}")
        raise AuthenticationError("Password change failed")


@router.post("/verify-email")
async def verify_email(
    request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
):
    """
    Send email verification.
    """
    try:
        if not credentials:
            raise AuthenticationError("Authentication required")

        # Use the provided ID token to send email verification
        id_token = credentials.credentials

        # Verify the token first
        decoded_token = firebase_config.verify_id_token(id_token)
        user_id = decoded_token["uid"]

        # Get user record to check if already verified
        user_record = firebase_config.get_user(user_id)

        if user_record.email_verified:
            return {"message": "Email is already verified"}

        # Send email verification using Firebase Auth REST API
        email_sent = firebase_config.send_email_verification(id_token)

        if email_sent:
            logger.info(f"Email verification sent to user: {user_id}")
            return {"message": "Email verification sent"}
        else:
            raise AuthenticationError("Failed to send email verification")

    except AuthenticationError:
        raise
    except Exception as e:
        logger.error(f"Email verification failed: {e}")
        raise AuthenticationError("Email verification failed")
