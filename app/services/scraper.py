"""
Web scraping service using Crawl4AI.
"""

import asyncio
import json
import time
from typing import Any, Dict, List, Optional, Union
from urllib.parse import urljoin, urlparse

from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode
from crawl4ai import JsonCssExtractionStrategy, LLMExtractionStrategy, CosineStrategy
from pydantic import BaseModel, Field, validator

from app.config.settings import settings
from app.core.exceptions import ScrapingError, ValidationError
from app.utils.logger import LoggerMixin


class ScrapingConfig(BaseModel):
    """Configuration for web scraping."""
    
    url: str = Field(..., description="URL to scrape")
    max_depth: int = Field(default=1, ge=0, le=5, description="Maximum crawling depth")
    max_pages: int = Field(default=10, ge=1, le=100, description="Maximum pages to scrape")
    delay: float = Field(default=1.0, ge=0.1, le=10.0, description="Delay between requests")
    timeout: int = Field(default=30, ge=5, le=120, description="Request timeout in seconds")
    user_agent: Optional[str] = Field(default=None, description="Custom user agent")
    headers: Optional[Dict[str, str]] = Field(default=None, description="Custom headers")
    cookies: Optional[Dict[str, str]] = Field(default=None, description="Custom cookies")
    proxy: Optional[str] = Field(default=None, description="Proxy URL")
    javascript: bool = Field(default=True, description="Enable JavaScript rendering")
    wait_for: Optional[str] = Field(default=None, description="CSS selector to wait for")
    screenshot: bool = Field(default=False, description="Take screenshot")
    pdf: bool = Field(default=False, description="Generate PDF")
    
    @validator("url")
    def validate_url(cls, v):
        """Validate URL format."""
        parsed = urlparse(v)
        if not parsed.scheme or not parsed.netloc:
            raise ValueError("Invalid URL format")
        return v
    
    @validator("proxy")
    def validate_proxy(cls, v):
        """Validate proxy URL format."""
        if v:
            parsed = urlparse(v)
            if not parsed.scheme or not parsed.netloc:
                raise ValueError("Invalid proxy URL format")
        return v


class ExtractionConfig(BaseModel):
    """Configuration for data extraction."""
    
    strategy: str = Field(default="css", description="Extraction strategy")
    selectors: Optional[Dict[str, str]] = Field(default=None, description="CSS selectors")
    schema: Optional[Dict[str, Any]] = Field(default=None, description="JSON schema for extraction")
    llm_prompt: Optional[str] = Field(default=None, description="LLM extraction prompt")
    similarity_threshold: float = Field(default=0.8, ge=0.0, le=1.0, description="Similarity threshold")
    
    @validator("strategy")
    def validate_strategy(cls, v):
        """Validate extraction strategy."""
        valid_strategies = ["css", "json", "llm", "cosine"]
        if v not in valid_strategies:
            raise ValueError(f"Strategy must be one of: {valid_strategies}")
        return v


class ScrapingResult(BaseModel):
    """Result of web scraping operation."""
    
    url: str
    title: Optional[str] = None
    content: Optional[str] = None
    extracted_data: Optional[Dict[str, Any]] = None
    links: Optional[List[str]] = None
    images: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None
    screenshot_path: Optional[str] = None
    pdf_path: Optional[str] = None
    error: Optional[str] = None
    timestamp: float
    duration: float


class WebScrapingService(LoggerMixin):
    """Web scraping service using Crawl4AI v0.7.4."""

    def __init__(self):
        pass

    async def __aenter__(self):
        """Async context manager entry."""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        pass
    
    async def scrape_url(
        self,
        config: ScrapingConfig,
        extraction_config: Optional[ExtractionConfig] = None,
    ) -> ScrapingResult:
        """Scrape a single URL using Crawl4AI v0.7.4."""
        start_time = time.time()
        
        try:
            # Create browser config
            browser_config = BrowserConfig(
                headless=True,
                user_agent=config.user_agent or "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                proxy=config.proxy,
                headers=config.headers,
                cookies=config.cookies,
                verbose=False,
            )

            # Create crawler run config
            run_config = CrawlerRunConfig(
                cache_mode=CacheMode.BYPASS,
                word_count_threshold=10,
                screenshot=config.screenshot,
                pdf=config.pdf,
                wait_for=config.wait_for,
                delay_before_return_html=config.delay,
            )

            # Set extraction strategy
            if extraction_config:
                extraction_strategy = self._get_extraction_strategy(extraction_config)
                if extraction_strategy:
                    run_config.extraction_strategy = extraction_strategy

            # Perform crawling
            self.logger.info(f"Starting to scrape URL: {config.url}")

            async with AsyncWebCrawler(config=browser_config) as crawler:
                result = await crawler.arun(url=config.url, config=run_config)
            
            # Process result
            duration = time.time() - start_time
            
            if result.success:
                # Extract links from result
                all_links = []
                if hasattr(result, 'links') and result.links:
                    if isinstance(result.links, dict):
                        # Extract internal and external links
                        internal_links = result.links.get("internal", [])
                        external_links = result.links.get("external", [])

                        # Convert link objects to href strings
                        for link in internal_links + external_links:
                            if isinstance(link, dict) and 'href' in link:
                                all_links.append(link['href'])
                            elif isinstance(link, str):
                                all_links.append(link)
                    elif isinstance(result.links, list):
                        # Convert link objects to href strings
                        for link in result.links:
                            if isinstance(link, dict) and 'href' in link:
                                all_links.append(link['href'])
                            elif isinstance(link, str):
                                all_links.append(link)

                # Extract images from result
                all_images = []
                if hasattr(result, 'media') and result.media:
                    if isinstance(result.media, dict):
                        images = result.media.get("images", [])
                        # Convert image objects to src strings
                        for img in images:
                            if isinstance(img, dict) and 'src' in img:
                                all_images.append(img['src'])
                            elif isinstance(img, str):
                                all_images.append(img)
                    elif isinstance(result.media, list):
                        # Convert image objects to src strings
                        for img in result.media:
                            if isinstance(img, dict) and 'src' in img:
                                all_images.append(img['src'])
                            elif isinstance(img, str):
                                all_images.append(img)

                scraping_result = ScrapingResult(
                    url=config.url,
                    title=result.metadata.get("title") if result.metadata else None,
                    content=result.markdown if hasattr(result, 'markdown') else result.cleaned_html,
                    extracted_data=result.extracted_content if hasattr(result, 'extracted_content') else None,
                    links=all_links,
                    images=all_images,
                    metadata=result.metadata if result.metadata else {},
                    screenshot_path=result.screenshot if config.screenshot else None,
                    pdf_path=result.pdf if config.pdf else None,
                    timestamp=start_time,
                    duration=duration,
                )
                
                self.logger.info(
                    f"Successfully scraped URL: {config.url}",
                    duration=duration,
                    content_length=len(result.markdown) if hasattr(result, 'markdown') and result.markdown else 0,
                )

                return scraping_result

            else:
                error_msg = f"Failed to scrape URL: {getattr(result, 'error_message', 'Unknown error')}"
                self.logger.error(error_msg)
                
                return ScrapingResult(
                    url=config.url,
                    error=error_msg,
                    timestamp=start_time,
                    duration=duration,
                )
        
        except Exception as e:
            duration = time.time() - start_time
            error_msg = f"Scraping error: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            
            return ScrapingResult(
                url=config.url,
                error=error_msg,
                timestamp=start_time,
                duration=duration,
            )
    
    async def scrape_multiple_urls(
        self,
        urls: List[str],
        config: ScrapingConfig,
        extraction_config: Optional[ExtractionConfig] = None,
        max_concurrent: int = 5,
    ) -> List[ScrapingResult]:
        """Scrape multiple URLs concurrently."""
        if not self.session_active:
            await self.start_session()
        
        self.logger.info(f"Starting to scrape {len(urls)} URLs")
        
        # Create semaphore for concurrency control
        semaphore = asyncio.Semaphore(min(max_concurrent, settings.crawl4ai_max_concurrent_requests))
        
        async def scrape_with_semaphore(url: str) -> ScrapingResult:
            async with semaphore:
                url_config = config.copy()
                url_config.url = url
                return await self.scrape_url(url_config, extraction_config)
        
        # Execute scraping tasks
        tasks = [scrape_with_semaphore(url) for url in urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                self.logger.error(f"Error scraping URL {urls[i]}: {result}")
                processed_results.append(
                    ScrapingResult(
                        url=urls[i],
                        error=str(result),
                        timestamp=time.time(),
                        duration=0,
                    )
                )
            else:
                processed_results.append(result)
        
        self.logger.info(f"Completed scraping {len(urls)} URLs")
        return processed_results
    
    def _get_extraction_strategy(self, config: ExtractionConfig):
        """Get extraction strategy based on configuration."""
        if config.strategy == "css" and config.selectors:
            return JsonCssExtractionStrategy(config.selectors)
        
        elif config.strategy == "json" and config.schema:
            return JsonCssExtractionStrategy(config.schema)
        
        elif config.strategy == "llm" and config.llm_prompt:
            return LLMExtractionStrategy(
                provider="openai",  # or other provider
                api_token=settings.openai_api_key,
                instruction=config.llm_prompt,
            )
        
        elif config.strategy == "cosine":
            return CosineStrategy(
                semantic_filter="",
                word_count_threshold=10,
                max_dist=config.similarity_threshold,
            )
        
        else:
            return None
    
    async def health_check(self) -> bool:
        """Check if scraping service is healthy."""
        try:
            # Try to scrape a simple page
            test_config = ScrapingConfig(url="https://httpbin.org/html")
            result = await self.scrape_url(test_config)

            return result.error is None

        except Exception as e:
            self.logger.error(f"Health check failed: {e}")
            return False
