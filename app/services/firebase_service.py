"""
Firebase service for database operations.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from google.cloud.firestore import Client as FirestoreClient
from google.cloud.firestore import Query

from app.config.firebase import firebase_config
from app.core.exceptions import DatabaseError, NotFoundError
from app.models.scraping_job import <PERSON><PERSON><PERSON><PERSON>ob, ScrapingJobFilter, ScrapingJobSummary
from app.models.user import User, UserQuota, UserSettings, UserUsageStats
from app.utils.logger import LoggerMixin


class FirebaseService(LoggerMixin):
    """Service for Firebase database operations."""
    
    def __init__(self):
        self.db: FirestoreClient = firebase_config.firestore
    
    # User operations
    async def create_user_profile(self, user: User) -> bool:
        """Create user profile in Firestore."""
        try:
            user_ref = self.db.collection("users").document(user.uid)
            user_data = user.dict()
            user_data["created_at"] = datetime.utcnow()
            user_data["updated_at"] = datetime.utcnow()
            
            user_ref.set(user_data)
            self.logger.info(f"User profile created: {user.uid}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to create user profile: {e}")
            raise DatabaseError(f"Failed to create user profile: {e}")
    
    async def get_user_profile(self, uid: str) -> Optional[User]:
        """Get user profile from Firestore."""
        try:
            user_ref = self.db.collection("users").document(uid)
            doc = user_ref.get()
            
            if doc.exists:
                data = doc.to_dict()
                return User(**data)
            
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to get user profile: {e}")
            raise DatabaseError(f"Failed to get user profile: {e}")
    
    async def update_user_profile(self, uid: str, updates: Dict[str, Any]) -> bool:
        """Update user profile in Firestore."""
        try:
            user_ref = self.db.collection("users").document(uid)
            updates["updated_at"] = datetime.utcnow()
            
            user_ref.update(updates)
            self.logger.info(f"User profile updated: {uid}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to update user profile: {e}")
            raise DatabaseError(f"Failed to update user profile: {e}")
    
    # User settings operations
    async def get_user_settings(self, uid: str) -> Optional[UserSettings]:
        """Get user settings."""
        try:
            settings_ref = self.db.collection("user_settings").document(uid)
            doc = settings_ref.get()
            
            if doc.exists:
                data = doc.to_dict()
                return UserSettings(**data)
            
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to get user settings: {e}")
            raise DatabaseError(f"Failed to get user settings: {e}")
    
    async def update_user_settings(self, uid: str, settings: UserSettings) -> bool:
        """Update user settings."""
        try:
            settings_ref = self.db.collection("user_settings").document(uid)
            settings_data = settings.dict()
            settings_data["updated_at"] = datetime.utcnow()
            
            settings_ref.set(settings_data)
            self.logger.info(f"User settings updated: {uid}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to update user settings: {e}")
            raise DatabaseError(f"Failed to update user settings: {e}")
    
    # User quota operations
    async def get_user_quota(self, uid: str) -> Optional[UserQuota]:
        """Get user quota."""
        try:
            quota_ref = self.db.collection("user_quotas").document(uid)
            doc = quota_ref.get()
            
            if doc.exists:
                data = doc.to_dict()
                return UserQuota(**data)
            
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to get user quota: {e}")
            raise DatabaseError(f"Failed to get user quota: {e}")
    
    async def update_user_quota(self, uid: str, quota: UserQuota) -> bool:
        """Update user quota."""
        try:
            quota_ref = self.db.collection("user_quotas").document(uid)
            quota_data = quota.dict()
            quota_data["updated_at"] = datetime.utcnow()
            
            quota_ref.set(quota_data)
            self.logger.info(f"User quota updated: {uid}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to update user quota: {e}")
            raise DatabaseError(f"Failed to update user quota: {e}")
    
    # Scraping job operations
    async def create_scraping_job(self, job: ScrapingJob) -> bool:
        """Create scraping job in Firestore."""
        try:
            job_ref = self.db.collection("scraping_jobs").document(job.job_id)
            job_data = job.dict()
            
            # Convert datetime objects to timestamps
            for key, value in job_data.items():
                if isinstance(value, datetime):
                    job_data[key] = value
            
            job_ref.set(job_data)
            self.logger.info(f"Scraping job created: {job.job_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to create scraping job: {e}")
            raise DatabaseError(f"Failed to create scraping job: {e}")
    
    async def get_scraping_job(self, job_id: str, user_id: str) -> Optional[ScrapingJob]:
        """Get scraping job by ID."""
        try:
            job_ref = self.db.collection("scraping_jobs").document(job_id)
            doc = job_ref.get()
            
            if doc.exists:
                data = doc.to_dict()
                
                # Verify job belongs to user
                if data.get("user_id") != user_id:
                    raise NotFoundError("Job not found")
                
                return ScrapingJob(**data)
            
            return None
            
        except NotFoundError:
            raise
        except Exception as e:
            self.logger.error(f"Failed to get scraping job: {e}")
            raise DatabaseError(f"Failed to get scraping job: {e}")
    
    async def update_scraping_job(self, job_id: str, updates: Dict[str, Any]) -> bool:
        """Update scraping job."""
        try:
            job_ref = self.db.collection("scraping_jobs").document(job_id)
            updates["updated_at"] = datetime.utcnow()
            
            job_ref.update(updates)
            self.logger.info(f"Scraping job updated: {job_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to update scraping job: {e}")
            raise DatabaseError(f"Failed to update scraping job: {e}")
    
    async def list_scraping_jobs(
        self, 
        user_id: str, 
        filters: ScrapingJobFilter
    ) -> List[ScrapingJobSummary]:
        """List user's scraping jobs with filters."""
        try:
            query = self.db.collection("scraping_jobs").where("user_id", "==", user_id)
            
            # Apply filters
            if filters.status:
                query = query.where("status", "==", filters.status.value)
            
            if filters.priority:
                query = query.where("priority", "==", filters.priority.value)
            
            if filters.created_after:
                query = query.where("created_at", ">=", filters.created_after)
            
            if filters.created_before:
                query = query.where("created_at", "<=", filters.created_before)
            
            # Apply sorting
            if filters.sort_order == "desc":
                query = query.order_by(filters.sort_by, direction=Query.DESCENDING)
            else:
                query = query.order_by(filters.sort_by, direction=Query.ASCENDING)
            
            # Apply pagination
            query = query.offset(filters.skip).limit(filters.limit)
            
            # Execute query
            docs = query.stream()
            
            jobs = []
            for doc in docs:
                data = doc.to_dict()
                
                # Filter by name if specified
                if filters.name_contains:
                    if filters.name_contains.lower() not in data.get("name", "").lower():
                        continue
                
                # Filter by tags if specified
                if filters.tags:
                    job_tags = data.get("tags", [])
                    if not any(tag in job_tags for tag in filters.tags):
                        continue
                
                # Create summary
                job_summary = ScrapingJobSummary(
                    job_id=data["job_id"],
                    name=data["name"],
                    status=data["status"],
                    priority=data["priority"],
                    progress_percentage=data.get("stats", {}).get("progress_percentage", 0.0),
                    success_rate=data.get("stats", {}).get("success_rate", 0.0),
                    total_urls=data.get("stats", {}).get("total_urls", 0),
                    processed_urls=data.get("stats", {}).get("processed_urls", 0),
                    created_at=data["created_at"],
                    updated_at=data["updated_at"],
                    tags=data.get("tags", []),
                )
                
                jobs.append(job_summary)
            
            return jobs
            
        except Exception as e:
            self.logger.error(f"Failed to list scraping jobs: {e}")
            raise DatabaseError(f"Failed to list scraping jobs: {e}")
    
    async def delete_scraping_job(self, job_id: str, user_id: str) -> bool:
        """Delete scraping job."""
        try:
            # First verify job belongs to user
            job = await self.get_scraping_job(job_id, user_id)
            if not job:
                raise NotFoundError("Job not found")
            
            # Delete the job
            job_ref = self.db.collection("scraping_jobs").document(job_id)
            job_ref.delete()
            
            self.logger.info(f"Scraping job deleted: {job_id}")
            return True
            
        except NotFoundError:
            raise
        except Exception as e:
            self.logger.error(f"Failed to delete scraping job: {e}")
            raise DatabaseError(f"Failed to delete scraping job: {e}")
    
    # Usage statistics operations
    async def get_user_usage_stats(self, uid: str, period: str) -> Optional[UserUsageStats]:
        """Get user usage statistics."""
        try:
            stats_ref = self.db.collection("usage_stats").document(f"{uid}_{period}")
            doc = stats_ref.get()
            
            if doc.exists:
                data = doc.to_dict()
                return UserUsageStats(**data)
            
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to get usage stats: {e}")
            raise DatabaseError(f"Failed to get usage stats: {e}")
    
    async def update_user_usage_stats(self, stats: UserUsageStats) -> bool:
        """Update user usage statistics."""
        try:
            stats_ref = self.db.collection("usage_stats").document(f"{stats.uid}_{stats.period}")
            stats_data = stats.dict()
            stats_data["updated_at"] = datetime.utcnow()
            
            stats_ref.set(stats_data)
            self.logger.info(f"Usage stats updated: {stats.uid}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to update usage stats: {e}")
            raise DatabaseError(f"Failed to update usage stats: {e}")


# Global Firebase service instance
firebase_service = FirebaseService()
