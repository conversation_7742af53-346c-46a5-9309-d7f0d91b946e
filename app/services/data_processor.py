"""
Data processing and transformation service.
"""

import json
import re
from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from urllib.parse import urljoin, urlparse

import pandas as pd
from bs4 import BeautifulSoup
from pydantic import BaseModel, Field

from app.core.exceptions import DataProcessingError
from app.utils.logger import LoggerMixin


class DataCleaningConfig(BaseModel):
    """Configuration for data cleaning operations."""
    
    remove_html: bool = Field(default=True, description="Remove HTML tags")
    normalize_whitespace: bool = Field(default=True, description="Normalize whitespace")
    remove_empty_fields: bool = Field(default=True, description="Remove empty fields")
    lowercase_text: bool = Field(default=False, description="Convert text to lowercase")
    remove_special_chars: bool = Field(default=False, description="Remove special characters")
    min_text_length: int = Field(default=0, ge=0, description="Minimum text length")
    max_text_length: Optional[int] = Field(default=None, description="Maximum text length")


class DataTransformConfig(BaseModel):
    """Configuration for data transformation operations."""
    
    output_format: str = Field(default="json", description="Output format")
    include_metadata: bool = Field(default=True, description="Include metadata")
    flatten_nested: bool = Field(default=False, description="Flatten nested objects")
    group_by_domain: bool = Field(default=False, description="Group results by domain")
    sort_by: Optional[str] = Field(default=None, description="Sort field")
    limit: Optional[int] = Field(default=None, description="Limit number of results")


class ProcessedData(BaseModel):
    """Processed data result."""
    
    data: Union[Dict[str, Any], List[Dict[str, Any]]]
    metadata: Dict[str, Any]
    processing_info: Dict[str, Any]
    timestamp: datetime


class DataProcessingService(LoggerMixin):
    """Service for processing and transforming scraped data."""
    
    def __init__(self):
        self.supported_formats = ["json", "csv", "xlsx", "xml", "yaml"]
    
    def clean_data(
        self,
        data: Union[Dict[str, Any], List[Dict[str, Any]]],
        config: DataCleaningConfig,
    ) -> Union[Dict[str, Any], List[Dict[str, Any]]]:
        """Clean and normalize data."""
        try:
            self.logger.info("Starting data cleaning process")
            
            if isinstance(data, list):
                cleaned_data = [self._clean_item(item, config) for item in data]
            else:
                cleaned_data = self._clean_item(data, config)
            
            # Remove empty items if configured
            if config.remove_empty_fields and isinstance(cleaned_data, list):
                cleaned_data = [item for item in cleaned_data if self._is_not_empty(item)]
            
            self.logger.info("Data cleaning completed successfully")
            return cleaned_data
            
        except Exception as e:
            self.logger.error(f"Data cleaning failed: {e}")
            raise DataProcessingError(f"Failed to clean data: {e}")
    
    def _clean_item(self, item: Dict[str, Any], config: DataCleaningConfig) -> Dict[str, Any]:
        """Clean a single data item."""
        cleaned_item = {}
        
        for key, value in item.items():
            if value is None:
                if not config.remove_empty_fields:
                    cleaned_item[key] = value
                continue
            
            if isinstance(value, str):
                cleaned_value = self._clean_text(value, config)
                if cleaned_value or not config.remove_empty_fields:
                    cleaned_item[key] = cleaned_value
            
            elif isinstance(value, (list, dict)):
                if isinstance(value, list):
                    cleaned_value = [
                        self._clean_item(v, config) if isinstance(v, dict)
                        else self._clean_text(str(v), config) if isinstance(v, str)
                        else v
                        for v in value
                    ]
                else:
                    cleaned_value = self._clean_item(value, config)
                
                if cleaned_value or not config.remove_empty_fields:
                    cleaned_item[key] = cleaned_value
            
            else:
                cleaned_item[key] = value
        
        return cleaned_item
    
    def _clean_text(self, text: str, config: DataCleaningConfig) -> str:
        """Clean text content."""
        if not text:
            return text
        
        # Remove HTML tags
        if config.remove_html:
            soup = BeautifulSoup(text, "html.parser")
            text = soup.get_text()
        
        # Normalize whitespace
        if config.normalize_whitespace:
            text = re.sub(r'\s+', ' ', text).strip()
        
        # Convert to lowercase
        if config.lowercase_text:
            text = text.lower()
        
        # Remove special characters
        if config.remove_special_chars:
            text = re.sub(r'[^\w\s]', '', text)
        
        # Apply length constraints
        if len(text) < config.min_text_length:
            return ""
        
        if config.max_text_length and len(text) > config.max_text_length:
            text = text[:config.max_text_length].rsplit(' ', 1)[0] + "..."
        
        return text
    
    def _is_not_empty(self, item: Dict[str, Any]) -> bool:
        """Check if item is not empty."""
        if not item:
            return False
        
        for value in item.values():
            if value is not None and value != "" and value != []:
                return True
        
        return False
    
    def transform_data(
        self,
        data: Union[Dict[str, Any], List[Dict[str, Any]]],
        config: DataTransformConfig,
    ) -> ProcessedData:
        """Transform data according to configuration."""
        try:
            self.logger.info("Starting data transformation")
            
            # Ensure data is a list
            if isinstance(data, dict):
                data_list = [data]
            else:
                data_list = data
            
            # Flatten nested objects if configured
            if config.flatten_nested:
                data_list = [self._flatten_dict(item) for item in data_list]
            
            # Group by domain if configured
            if config.group_by_domain:
                data_list = self._group_by_domain(data_list)
            
            # Sort data if configured
            if config.sort_by:
                data_list = self._sort_data(data_list, config.sort_by)
            
            # Limit results if configured
            if config.limit:
                data_list = data_list[:config.limit]
            
            # Prepare metadata
            metadata = {
                "total_items": len(data_list),
                "processing_config": config.dict(),
                "timestamp": datetime.utcnow().isoformat(),
            }
            
            # Add metadata to items if configured
            if config.include_metadata:
                for item in data_list:
                    if isinstance(item, dict):
                        item["_metadata"] = {
                            "processed_at": datetime.utcnow().isoformat(),
                            "processor_version": "1.0.0",
                        }
            
            # Format output
            if config.output_format == "json":
                output_data = data_list if isinstance(data, list) else data_list[0]
            elif config.output_format == "csv":
                output_data = self._to_csv(data_list)
            elif config.output_format == "xlsx":
                output_data = self._to_excel(data_list)
            else:
                output_data = data_list
            
            result = ProcessedData(
                data=output_data,
                metadata=metadata,
                processing_info={
                    "cleaning_applied": False,
                    "transformation_applied": True,
                    "output_format": config.output_format,
                },
                timestamp=datetime.utcnow(),
            )
            
            self.logger.info("Data transformation completed successfully")
            return result
            
        except Exception as e:
            self.logger.error(f"Data transformation failed: {e}")
            raise DataProcessingError(f"Failed to transform data: {e}")
    
    def _flatten_dict(self, d: Dict[str, Any], parent_key: str = "", sep: str = "_") -> Dict[str, Any]:
        """Flatten nested dictionary."""
        items = []
        for k, v in d.items():
            new_key = f"{parent_key}{sep}{k}" if parent_key else k
            if isinstance(v, dict):
                items.extend(self._flatten_dict(v, new_key, sep=sep).items())
            else:
                items.append((new_key, v))
        return dict(items)
    
    def _group_by_domain(self, data_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Group data by domain."""
        grouped = {}
        
        for item in data_list:
            url = item.get("url", "")
            if url:
                domain = urlparse(url).netloc
                if domain not in grouped:
                    grouped[domain] = []
                grouped[domain].append(item)
        
        return [{"domain": domain, "items": items} for domain, items in grouped.items()]
    
    def _sort_data(self, data_list: List[Dict[str, Any]], sort_by: str) -> List[Dict[str, Any]]:
        """Sort data by specified field."""
        try:
            return sorted(data_list, key=lambda x: x.get(sort_by, ""))
        except Exception as e:
            self.logger.warning(f"Failed to sort by {sort_by}: {e}")
            return data_list
    
    def _to_csv(self, data_list: List[Dict[str, Any]]) -> str:
        """Convert data to CSV format."""
        if not data_list:
            return ""
        
        df = pd.DataFrame(data_list)
        return df.to_csv(index=False)
    
    def _to_excel(self, data_list: List[Dict[str, Any]]) -> bytes:
        """Convert data to Excel format."""
        if not data_list:
            return b""
        
        df = pd.DataFrame(data_list)
        return df.to_excel(index=False, engine='openpyxl')
    
    def extract_structured_data(self, html_content: str) -> Dict[str, Any]:
        """Extract structured data from HTML content."""
        try:
            soup = BeautifulSoup(html_content, "html.parser")
            structured_data = {}
            
            # Extract JSON-LD
            json_ld_scripts = soup.find_all("script", type="application/ld+json")
            if json_ld_scripts:
                json_ld_data = []
                for script in json_ld_scripts:
                    try:
                        data = json.loads(script.string)
                        json_ld_data.append(data)
                    except json.JSONDecodeError:
                        continue
                structured_data["json_ld"] = json_ld_data
            
            # Extract Open Graph data
            og_data = {}
            og_tags = soup.find_all("meta", property=re.compile(r"^og:"))
            for tag in og_tags:
                property_name = tag.get("property", "").replace("og:", "")
                content = tag.get("content", "")
                if property_name and content:
                    og_data[property_name] = content
            
            if og_data:
                structured_data["open_graph"] = og_data
            
            # Extract Twitter Card data
            twitter_data = {}
            twitter_tags = soup.find_all("meta", attrs={"name": re.compile(r"^twitter:")})
            for tag in twitter_tags:
                name = tag.get("name", "").replace("twitter:", "")
                content = tag.get("content", "")
                if name and content:
                    twitter_data[name] = content
            
            if twitter_data:
                structured_data["twitter_card"] = twitter_data
            
            return structured_data
            
        except Exception as e:
            self.logger.error(f"Failed to extract structured data: {e}")
            return {}
    
    def validate_data(self, data: Any, schema: Dict[str, Any]) -> bool:
        """Validate data against a schema."""
        try:
            # Simple validation - can be extended with jsonschema
            if not isinstance(data, dict):
                return False
            
            required_fields = schema.get("required", [])
            for field in required_fields:
                if field not in data:
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Data validation failed: {e}")
            return False
