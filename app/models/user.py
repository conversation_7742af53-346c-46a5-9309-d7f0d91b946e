"""
User models and schemas.
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, EmailStr, Field, validator


class UserRole(str, Enum):
    """User roles."""
    ADMIN = "admin"
    USER = "user"
    VIEWER = "viewer"


class UserStatus(str, Enum):
    """User status."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"


class UserBase(BaseModel):
    """Base user model."""
    email: EmailStr
    display_name: Optional[str] = None
    role: UserRole = UserRole.USER
    status: UserStatus = UserStatus.ACTIVE


class UserCreate(UserBase):
    """User creation model."""
    password: str = Field(..., min_length=8)
    
    @validator("password")
    def validate_password(cls, v):
        """Validate password strength."""
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters long")
        
        if not any(c.isupper() for c in v):
            raise ValueError("Password must contain at least one uppercase letter")
        
        if not any(c.islower() for c in v):
            raise ValueError("Password must contain at least one lowercase letter")
        
        if not any(c.isdigit() for c in v):
            raise ValueError("Password must contain at least one digit")
        
        return v


class UserUpdate(BaseModel):
    """User update model."""
    display_name: Optional[str] = None
    role: Optional[UserRole] = None
    status: Optional[UserStatus] = None


class UserInDB(UserBase):
    """User model as stored in database."""
    uid: str
    created_at: datetime
    updated_at: datetime
    last_login: Optional[datetime] = None
    email_verified: bool = False
    phone_number: Optional[str] = None
    photo_url: Optional[str] = None
    custom_claims: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None


class User(UserInDB):
    """User model for API responses."""
    pass


class UserProfile(BaseModel):
    """User profile information."""
    uid: str
    email: EmailStr
    display_name: Optional[str] = None
    role: UserRole
    status: UserStatus
    created_at: datetime
    last_login: Optional[datetime] = None
    email_verified: bool
    photo_url: Optional[str] = None
    
    # Usage statistics
    total_scraping_jobs: int = 0
    total_data_processed: int = 0
    api_calls_this_month: int = 0
    storage_used_mb: float = 0.0


class UserSettings(BaseModel):
    """User settings and preferences."""
    uid: str
    
    # Notification preferences
    email_notifications: bool = True
    job_completion_notifications: bool = True
    error_notifications: bool = True
    
    # API preferences
    default_rate_limit: int = 60
    default_timeout: int = 30
    
    # Data preferences
    default_output_format: str = "json"
    auto_cleanup_days: int = 30
    
    # UI preferences
    theme: str = "light"
    timezone: str = "UTC"
    language: str = "en"
    
    updated_at: datetime


class UserApiKey(BaseModel):
    """User API key model."""
    key_id: str
    uid: str
    name: str
    key_hash: str  # Hashed version of the key
    permissions: List[str] = []
    rate_limit: int = 60
    expires_at: Optional[datetime] = None
    created_at: datetime
    last_used: Optional[datetime] = None
    is_active: bool = True


class UserApiKeyCreate(BaseModel):
    """API key creation model."""
    name: str = Field(..., min_length=1, max_length=100)
    permissions: List[str] = []
    rate_limit: int = Field(default=60, ge=1, le=1000)
    expires_in_days: Optional[int] = Field(default=None, ge=1, le=365)


class UserApiKeyResponse(BaseModel):
    """API key response model."""
    key_id: str
    name: str
    api_key: str  # Only returned once during creation
    permissions: List[str]
    rate_limit: int
    expires_at: Optional[datetime]
    created_at: datetime


class UserUsageStats(BaseModel):
    """User usage statistics."""
    uid: str
    period: str  # "daily", "weekly", "monthly"
    
    # Scraping statistics
    scraping_jobs_count: int = 0
    successful_jobs_count: int = 0
    failed_jobs_count: int = 0
    total_pages_scraped: int = 0
    total_data_extracted_mb: float = 0.0
    
    # API statistics
    api_calls_count: int = 0
    api_errors_count: int = 0
    
    # Resource usage
    processing_time_seconds: float = 0.0
    storage_used_mb: float = 0.0
    bandwidth_used_mb: float = 0.0
    
    # Time period
    period_start: datetime
    period_end: datetime
    updated_at: datetime


class UserQuota(BaseModel):
    """User quota and limits."""
    uid: str
    
    # Monthly limits
    max_scraping_jobs_per_month: int = 100
    max_pages_per_month: int = 1000
    max_api_calls_per_month: int = 10000
    max_storage_mb: float = 1000.0
    max_concurrent_jobs: int = 5
    
    # Current usage
    current_scraping_jobs: int = 0
    current_pages_scraped: int = 0
    current_api_calls: int = 0
    current_storage_mb: float = 0.0
    current_concurrent_jobs: int = 0
    
    # Reset date
    quota_reset_date: datetime
    updated_at: datetime
    
    @property
    def scraping_jobs_remaining(self) -> int:
        """Get remaining scraping jobs."""
        return max(0, self.max_scraping_jobs_per_month - self.current_scraping_jobs)
    
    @property
    def pages_remaining(self) -> int:
        """Get remaining pages."""
        return max(0, self.max_pages_per_month - self.current_pages_scraped)
    
    @property
    def api_calls_remaining(self) -> int:
        """Get remaining API calls."""
        return max(0, self.max_api_calls_per_month - self.current_api_calls)
    
    @property
    def storage_remaining_mb(self) -> float:
        """Get remaining storage."""
        return max(0.0, self.max_storage_mb - self.current_storage_mb)
