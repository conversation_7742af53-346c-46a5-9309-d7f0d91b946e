"""
Scraping job models and schemas.
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, HttpUrl, validator


class JobStatus(str, Enum):
    """Job status enumeration."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"


class JobPriority(str, Enum):
    """Job priority enumeration."""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


class ScrapingJobConfig(BaseModel):
    """Scraping job configuration."""
    
    # Target configuration
    urls: List[HttpUrl] = Field(..., min_items=1, max_items=100)
    max_depth: int = Field(default=1, ge=0, le=5)
    max_pages: int = Field(default=10, ge=1, le=1000)
    
    # Request configuration
    delay: float = Field(default=1.0, ge=0.1, le=10.0)
    timeout: int = Field(default=30, ge=5, le=120)
    user_agent: Optional[str] = None
    headers: Optional[Dict[str, str]] = None
    cookies: Optional[Dict[str, str]] = None
    proxy: Optional[str] = None
    
    # Browser configuration
    javascript: bool = Field(default=True)
    wait_for: Optional[str] = None
    screenshot: bool = Field(default=False)
    pdf: bool = Field(default=False)
    
    # Extraction configuration
    extraction_strategy: str = Field(default="css")
    css_selectors: Optional[Dict[str, str]] = None
    json_schema: Optional[Dict[str, Any]] = None
    llm_prompt: Optional[str] = None
    similarity_threshold: float = Field(default=0.8, ge=0.0, le=1.0)
    
    # Data processing configuration
    clean_data: bool = Field(default=True)
    remove_html: bool = Field(default=True)
    normalize_whitespace: bool = Field(default=True)
    min_text_length: int = Field(default=0, ge=0)
    max_text_length: Optional[int] = None
    
    # Output configuration
    output_format: str = Field(default="json")
    include_metadata: bool = Field(default=True)
    flatten_nested: bool = Field(default=False)
    
    @validator("extraction_strategy")
    def validate_extraction_strategy(cls, v):
        """Validate extraction strategy."""
        valid_strategies = ["css", "json", "llm", "cosine"]
        if v not in valid_strategies:
            raise ValueError(f"Strategy must be one of: {valid_strategies}")
        return v
    
    @validator("output_format")
    def validate_output_format(cls, v):
        """Validate output format."""
        valid_formats = ["json", "csv", "xlsx", "xml"]
        if v not in valid_formats:
            raise ValueError(f"Format must be one of: {valid_formats}")
        return v


class ScrapingJobCreate(BaseModel):
    """Scraping job creation model."""
    name: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=1000)
    config: ScrapingJobConfig
    priority: JobPriority = JobPriority.NORMAL
    scheduled_at: Optional[datetime] = None
    tags: List[str] = Field(default=[], max_items=10)


class ScrapingJobUpdate(BaseModel):
    """Scraping job update model."""
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=1000)
    priority: Optional[JobPriority] = None
    scheduled_at: Optional[datetime] = None
    tags: Optional[List[str]] = Field(None, max_items=10)


class ScrapingJobResult(BaseModel):
    """Individual scraping result."""
    url: str
    title: Optional[str] = None
    content: Optional[str] = None
    extracted_data: Optional[Dict[str, Any]] = None
    links: Optional[List[str]] = None
    images: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None
    screenshot_path: Optional[str] = None
    pdf_path: Optional[str] = None
    error: Optional[str] = None
    timestamp: datetime
    duration: float


class ScrapingJobStats(BaseModel):
    """Job execution statistics."""
    total_urls: int = 0
    processed_urls: int = 0
    successful_urls: int = 0
    failed_urls: int = 0
    total_pages_scraped: int = 0
    total_data_extracted_mb: float = 0.0
    average_processing_time: float = 0.0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    total_duration: Optional[float] = None


class ScrapingJob(BaseModel):
    """Complete scraping job model."""
    job_id: str
    user_id: str
    name: str
    description: Optional[str] = None
    config: ScrapingJobConfig
    status: JobStatus
    priority: JobPriority
    
    # Execution details
    results: List[ScrapingJobResult] = []
    stats: ScrapingJobStats = ScrapingJobStats()
    error_message: Optional[str] = None
    
    # Scheduling
    scheduled_at: Optional[datetime] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    # Metadata
    tags: List[str] = []
    created_at: datetime
    updated_at: datetime
    
    # Storage
    output_file_path: Optional[str] = None
    output_file_size_mb: Optional[float] = None
    
    @property
    def is_completed(self) -> bool:
        """Check if job is completed."""
        return self.status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED]
    
    @property
    def is_running(self) -> bool:
        """Check if job is currently running."""
        return self.status == JobStatus.RUNNING
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate."""
        if self.stats.processed_urls == 0:
            return 0.0
        return (self.stats.successful_urls / self.stats.processed_urls) * 100
    
    @property
    def progress_percentage(self) -> float:
        """Calculate progress percentage."""
        if self.stats.total_urls == 0:
            return 0.0
        return (self.stats.processed_urls / self.stats.total_urls) * 100


class ScrapingJobSummary(BaseModel):
    """Scraping job summary for list views."""
    job_id: str
    name: str
    status: JobStatus
    priority: JobPriority
    progress_percentage: float
    success_rate: float
    total_urls: int
    processed_urls: int
    created_at: datetime
    updated_at: datetime
    tags: List[str] = []


class ScrapingJobFilter(BaseModel):
    """Filter parameters for job queries."""
    status: Optional[JobStatus] = None
    priority: Optional[JobPriority] = None
    user_id: Optional[str] = None
    tags: Optional[List[str]] = None
    created_after: Optional[datetime] = None
    created_before: Optional[datetime] = None
    name_contains: Optional[str] = None
    
    # Pagination
    skip: int = Field(default=0, ge=0)
    limit: int = Field(default=20, ge=1, le=100)
    
    # Sorting
    sort_by: str = Field(default="created_at")
    sort_order: str = Field(default="desc")
    
    @validator("sort_by")
    def validate_sort_by(cls, v):
        """Validate sort field."""
        valid_fields = [
            "created_at", "updated_at", "name", "status", 
            "priority", "progress_percentage", "success_rate"
        ]
        if v not in valid_fields:
            raise ValueError(f"Sort field must be one of: {valid_fields}")
        return v
    
    @validator("sort_order")
    def validate_sort_order(cls, v):
        """Validate sort order."""
        if v not in ["asc", "desc"]:
            raise ValueError("Sort order must be 'asc' or 'desc'")
        return v


class JobSchedule(BaseModel):
    """Job scheduling configuration."""
    job_id: str
    user_id: str
    
    # Schedule type
    schedule_type: str = Field(...)  # "once", "recurring"
    
    # One-time schedule
    run_at: Optional[datetime] = None
    
    # Recurring schedule
    cron_expression: Optional[str] = None
    timezone: str = Field(default="UTC")
    
    # Recurrence settings
    is_active: bool = Field(default=True)
    max_runs: Optional[int] = None
    current_runs: int = Field(default=0)
    
    # Metadata
    created_at: datetime
    updated_at: datetime
    next_run_at: Optional[datetime] = None
    last_run_at: Optional[datetime] = None
    
    @validator("schedule_type")
    def validate_schedule_type(cls, v):
        """Validate schedule type."""
        if v not in ["once", "recurring"]:
            raise ValueError("Schedule type must be 'once' or 'recurring'")
        return v
