# Application Settings
APP_NAME=Crawl Agent
APP_VERSION=1.0.0
DEBUG=True
ENVIRONMENT=development

# Server Configuration
HOST=0.0.0.0
PORT=8000
WORKERS=4

# Security
SECRET_KEY=your-super-secret-key-change-this-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALGORITHM=HS256

# Firebase Configuration
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_PRIVATE_KEY_ID=your-private-key-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=your-client-id
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token
FIREBASE_AUTH_PROVIDER_X509_CERT_URL=https://www.googleapis.com/oauth2/v1/certs
FIREBASE_CLIENT_X509_CERT_URL=https://www.googleapis.com/robot/v1/metadata/x509/your-service-account%40your-project.iam.gserviceaccount.com
FIREBASE_WEB_API_KEY=your-web-api-key

# Database Configuration
FIRESTORE_DATABASE_ID=(default)

# Redis Configuration (for task queue)
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=

# Crawl4AI Configuration
CRAWL4AI_MAX_CONCURRENT_REQUESTS=10
CRAWL4AI_REQUEST_TIMEOUT=30
CRAWL4AI_USER_AGENT=CrawlAgent/1.0.0
CRAWL4AI_RESPECT_ROBOTS_TXT=True

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_BURST=10

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE=logs/app.log

# Monitoring
ENABLE_METRICS=True
METRICS_PORT=9090

# CORS Settings
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]
CORS_ALLOW_CREDENTIALS=True
CORS_ALLOW_METHODS=["GET", "POST", "PUT", "DELETE", "OPTIONS"]
CORS_ALLOW_HEADERS=["*"]

# File Storage
MAX_FILE_SIZE_MB=100
ALLOWED_FILE_TYPES=["pdf", "html", "txt", "csv", "json"]

# Background Tasks
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2
CELERY_TASK_SERIALIZER=json
CELERY_RESULT_SERIALIZER=json
CELERY_ACCEPT_CONTENT=["json"]
CELERY_TIMEZONE=UTC

# External APIs
OPENAI_API_KEY=your-openai-api-key-if-needed
ANTHROPIC_API_KEY=your-anthropic-api-key-if-needed

# Proxy Settings (optional)
HTTP_PROXY=
HTTPS_PROXY=
NO_PROXY=localhost,127.0.0.1

# Development Settings
RELOAD=True
DEBUG_SQL=False
