#!/usr/bin/env python3
"""
Firebase setup script for Crawl Agent.

This script helps set up Firebase configuration and initial data.
"""

import json
import os
import sys
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.config.firebase import firebase_config
from app.config.settings import settings
from app.utils.logger import setup_logging, get_logger

setup_logging()
logger = get_logger(__name__)


def check_firebase_credentials():
    """Check if Firebase credentials are properly configured."""
    try:
        required_vars = [
            "FIREBASE_PROJECT_ID",
            "FIREBASE_PRIVATE_KEY_ID",
            "FIREBASE_PRIVATE_KEY",
            "FIREBASE_CLIENT_EMAIL",
            "FIREBASE_CLIENT_ID",
        ]
        
        missing_vars = []
        for var in required_vars:
            if not getattr(settings, var.lower(), None):
                missing_vars.append(var)
        
        if missing_vars:
            logger.error(f"Missing Firebase environment variables: {missing_vars}")
            return False
        
        logger.info("Firebase credentials configuration check passed")
        return True
        
    except Exception as e:
        logger.error(f"Error checking Firebase credentials: {e}")
        return False


def test_firebase_connection():
    """Test Firebase connection."""
    try:
        logger.info("Testing Firebase connection...")
        
        # Initialize Firebase
        firebase_config.initialize()
        
        # Test Firestore connection
        db = firebase_config.firestore
        
        # Try to write and read a test document
        test_doc_ref = db.collection("health_check").document("test")
        test_doc_ref.set({
            "message": "Firebase connection test",
            "timestamp": "2024-01-01T00:00:00Z",
        })
        
        # Read the document back
        doc = test_doc_ref.get()
        if doc.exists:
            logger.info("Firebase Firestore connection successful")
            
            # Clean up test document
            test_doc_ref.delete()
            
            return True
        else:
            logger.error("Failed to read test document from Firestore")
            return False
            
    except Exception as e:
        logger.error(f"Firebase connection test failed: {e}")
        return False


def setup_firestore_collections():
    """Set up initial Firestore collections and indexes."""
    try:
        logger.info("Setting up Firestore collections...")
        
        db = firebase_config.firestore
        
        # Create collections with initial documents
        collections_to_create = [
            "users",
            "scraping_jobs",
            "user_settings",
            "user_quotas",
            "api_keys",
            "usage_stats",
        ]
        
        for collection_name in collections_to_create:
            # Create a placeholder document to ensure collection exists
            doc_ref = db.collection(collection_name).document("_placeholder")
            doc_ref.set({
                "created_at": "2024-01-01T00:00:00Z",
                "description": f"Placeholder document for {collection_name} collection",
                "delete_me": True,
            })
            logger.info(f"Created collection: {collection_name}")
        
        logger.info("Firestore collections setup completed")
        return True
        
    except Exception as e:
        logger.error(f"Failed to setup Firestore collections: {e}")
        return False


def setup_security_rules():
    """Display security rules that should be applied to Firestore."""
    logger.info("Firestore Security Rules (apply these in Firebase Console):")
    
    security_rules = """
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Users can only access their own scraping jobs
    match /scraping_jobs/{jobId} {
      allow read, write: if request.auth != null && 
        resource.data.user_id == request.auth.uid;
    }
    
    // Users can only access their own settings
    match /user_settings/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Users can only access their own quotas
    match /user_quotas/{userId} {
      allow read: if request.auth != null && request.auth.uid == userId;
      allow write: if request.auth != null && 
        request.auth.token.role == 'admin';
    }
    
    // Users can only access their own API keys
    match /api_keys/{keyId} {
      allow read, write: if request.auth != null && 
        resource.data.uid == request.auth.uid;
    }
    
    // Users can only access their own usage stats
    match /usage_stats/{userId} {
      allow read: if request.auth != null && request.auth.uid == userId;
      allow write: if request.auth != null && 
        request.auth.token.role == 'admin';
    }
    
    // Health check collection (public read for monitoring)
    match /health_check/{document} {
      allow read: if true;
      allow write: if request.auth != null;
    }
  }
}
"""
    
    print(security_rules)
    
    # Save rules to file
    rules_file = Path(__file__).parent.parent / "firestore.rules"
    with open(rules_file, "w") as f:
        f.write(security_rules)
    
    logger.info(f"Security rules saved to: {rules_file}")


def create_admin_user():
    """Create an admin user (interactive)."""
    try:
        print("\n=== Create Admin User ===")
        email = input("Enter admin email: ").strip()
        password = input("Enter admin password: ").strip()
        display_name = input("Enter admin display name: ").strip()
        
        if not email or not password:
            logger.error("Email and password are required")
            return False
        
        # Create user in Firebase Auth
        user_record = firebase_config.create_user(
            email=email,
            password=password,
            display_name=display_name or "Admin User",
            email_verified=True,
        )
        
        # Set admin custom claims
        firebase_config.set_custom_claims(user_record.uid, {
            "role": "admin",
            "status": "active",
            "created_at": "2024-01-01T00:00:00Z",
        })
        
        logger.info(f"Admin user created successfully: {user_record.uid}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to create admin user: {e}")
        return False


def main():
    """Main setup function."""
    print("=== Firebase Setup for Crawl Agent ===\n")
    
    # Check credentials
    if not check_firebase_credentials():
        print("❌ Firebase credentials check failed")
        print("Please ensure all required environment variables are set in .env file")
        return False
    
    print("✅ Firebase credentials check passed")
    
    # Test connection
    if not test_firebase_connection():
        print("❌ Firebase connection test failed")
        return False
    
    print("✅ Firebase connection test passed")
    
    # Setup collections
    if not setup_firestore_collections():
        print("❌ Firestore collections setup failed")
        return False
    
    print("✅ Firestore collections setup completed")
    
    # Display security rules
    setup_security_rules()
    print("✅ Security rules generated")
    
    # Ask if user wants to create admin user
    create_admin = input("\nDo you want to create an admin user? (y/N): ").strip().lower()
    if create_admin in ['y', 'yes']:
        if create_admin_user():
            print("✅ Admin user created successfully")
        else:
            print("❌ Admin user creation failed")
    
    print("\n=== Firebase Setup Completed ===")
    print("Next steps:")
    print("1. Apply the security rules in Firebase Console")
    print("2. Set up Firebase Storage bucket (if needed)")
    print("3. Configure Firebase Auth providers (if needed)")
    print("4. Start the application with: uvicorn app.main:app --reload")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
