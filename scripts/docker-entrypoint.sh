#!/bin/bash

# Function to log messages
log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1"
}

log "Starting Docker entrypoint script..."

# Ensure directories exist (they should be created by Docker volumes)
log "Setting up directories..."
mkdir -p /app/logs /app/data /app/temp /app/uploads /app/downloads

# Test write access to logs directory (bind mount)
if touch "/app/logs/.test_write" 2>/dev/null; then
    rm -f "/app/logs/.test_write"
    log "Logs directory is writable"
else
    log "Warning: Logs directory is not writable, using console logging only"
fi

log "Directory setup completed"

# Execute the main command
log "Executing command: $@"
exec "$@"
