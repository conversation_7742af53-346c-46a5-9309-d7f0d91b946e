#!/bin/bash
set -e

# Function to log messages
log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1"
}

log "Starting Docker entrypoint script..."

# Ensure directories exist and have correct permissions
log "Setting up directories and permissions..."
mkdir -p /app/logs /app/data /app/temp /app/uploads /app/downloads

# Set permissions for directories that might be mounted as volumes
if [ -w /app/logs ]; then
    chmod 775 /app/logs
    log "Set permissions for logs directory"
    # Test write access
    if touch /app/logs/test_write.log 2>/dev/null; then
        rm -f /app/logs/test_write.log
        log "Logs directory is writable"
    else
        log "Warning: Cannot write to logs directory, using console logging only"
    fi
else
    log "Warning: Cannot write to logs directory, using console logging only"
fi

if [ -w /app/data ]; then
    chmod 775 /app/data
    log "Set permissions for data directory"
fi

if [ -w /app/temp ]; then
    chmod 775 /app/temp
    log "Set permissions for temp directory"
fi

if [ -w /app/uploads ]; then
    chmod 775 /app/uploads
    log "Set permissions for uploads directory"
fi

if [ -w /app/downloads ]; then
    chmod 775 /app/downloads
    log "Set permissions for downloads directory"
fi

log "Directory setup completed"

# Execute the main command
log "Executing command: $@"
exec "$@"
