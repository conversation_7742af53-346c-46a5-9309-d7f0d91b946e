#!/usr/bin/env python3
"""
Interactive Firebase setup and configuration script for Crawl Agent.
"""

import json
import os
import sys
import secrets
from pathlib import Path
from typing import Dict, Any


def print_header():
    """Print setup header."""
    print("🔥" * 50)
    print("🔥  FIREBASE SETUP FOR CRAWL AGENT")
    print("🔥" * 50)
    print()


def print_step(step_num: int, title: str):
    """Print step header."""
    print(f"\n📋 Step {step_num}: {title}")
    print("-" * 40)


def get_user_input(prompt: str, default: str = None, required: bool = True) -> str:
    """Get user input with optional default."""
    if default:
        full_prompt = f"{prompt} [{default}]: "
    else:
        full_prompt = f"{prompt}: "
    
    while True:
        value = input(full_prompt).strip()
        if value:
            return value
        elif default:
            return default
        elif not required:
            return ""
        else:
            print("❌ This field is required. Please enter a value.")


def yes_no_prompt(prompt: str, default: bool = True) -> bool:
    """Get yes/no input from user."""
    default_str = "Y/n" if default else "y/N"
    while True:
        response = input(f"{prompt} [{default_str}]: ").strip().lower()
        if response in ['y', 'yes']:
            return True
        elif response in ['n', 'no']:
            return False
        elif response == "":
            return default
        else:
            print("Please enter 'y' or 'n'")


def load_service_account_json() -> Dict[str, Any]:
    """Load service account JSON file."""
    print("\n🔑 Firebase Service Account Setup")
    print("You need to download the service account JSON file from Firebase Console:")
    print("1. Go to Firebase Console → Project Settings → Service Accounts")
    print("2. Click 'Generate new private key'")
    print("3. Download the JSON file")
    print()
    
    while True:
        json_path = get_user_input("Enter path to service account JSON file")
        json_file = Path(json_path)
        
        if not json_file.exists():
            print(f"❌ File not found: {json_path}")
            continue
        
        try:
            with open(json_file, 'r') as f:
                service_account = json.load(f)
            
            # Validate required fields
            required_fields = [
                'project_id', 'private_key_id', 'private_key', 
                'client_email', 'client_id'
            ]
            
            missing_fields = [field for field in required_fields if field not in service_account]
            if missing_fields:
                print(f"❌ Missing required fields in JSON: {missing_fields}")
                continue
            
            print("✅ Service account JSON loaded successfully!")
            print(f"   Project ID: {service_account['project_id']}")
            print(f"   Service Account: {service_account['client_email']}")
            return service_account
            
        except json.JSONDecodeError:
            print("❌ Invalid JSON file. Please check the file format.")
        except Exception as e:
            print(f"❌ Error reading file: {e}")


def get_web_api_key() -> str:
    """Get Firebase Web API key."""
    print("\n🌐 Firebase Web API Key")
    print("You can find this in Firebase Console → Project Settings → General tab")
    print("Look for 'Web API Key' in the 'Your apps' section")
    print("If you don't have a web app, click 'Add app' and select 'Web'")
    print()
    
    return get_user_input("Enter Firebase Web API Key")


def create_env_file(service_account: Dict[str, Any], web_api_key: str) -> bool:
    """Create .env file with Firebase configuration."""
    print_step(3, "Creating .env file")
    
    # Check if .env already exists
    env_file = Path(".env")
    if env_file.exists():
        if not yes_no_prompt("⚠️  .env file already exists. Overwrite?", default=False):
            backup_path = Path(".env.backup")
            env_file.rename(backup_path)
            print(f"✅ Existing .env backed up to {backup_path}")
    
    # Read template
    template_file = Path(".env.example")
    if not template_file.exists():
        print("❌ .env.example not found!")
        return False
    
    with open(template_file, 'r') as f:
        template_content = f.read()
    
    # Prepare private key (escape newlines)
    private_key = service_account['private_key'].replace('\n', '\\n')
    
    # Create client cert URL
    client_email_encoded = service_account['client_email'].replace('@', '%40')
    cert_url = f"https://www.googleapis.com/robot/v1/metadata/x509/{client_email_encoded}"
    
    # Firebase replacements
    firebase_replacements = {
        'your-firebase-project-id': service_account['project_id'],
        'your-private-key-id': service_account['private_key_id'],
        'YOUR_PRIVATE_KEY_HERE': private_key,
        '<EMAIL>': service_account['client_email'],
        'your-client-id': service_account['client_id'],
        'your-web-api-key': web_api_key,
        'https://www.googleapis.com/robot/v1/metadata/x509/your-service-account%40your-project.iam.gserviceaccount.com': cert_url,
    }
    
    # Apply replacements
    env_content = template_content
    for old_value, new_value in firebase_replacements.items():
        env_content = env_content.replace(old_value, str(new_value))
    
    # Generate a secure secret key
    secret_key = secrets.token_urlsafe(32)
    env_content = env_content.replace('your-super-secret-key-change-this-in-production', secret_key)
    
    # Write .env file
    with open(env_file, 'w') as f:
        f.write(env_content)
    
    print("✅ .env file created successfully!")
    print(f"   Project ID: {service_account['project_id']}")
    print(f"   Service Account: {service_account['client_email']}")
    print(f"   Secret Key: Generated automatically")
    return True


def test_firebase_connection() -> bool:
    """Test Firebase connection."""
    print_step(4, "Testing Firebase Connection")
    
    try:
        # Add the app directory to Python path
        sys.path.insert(0, str(Path(__file__).parent.parent))
        
        # Import after adding to path
        from app.config.settings import settings
        
        print("✅ Settings loaded successfully")
        print(f"   Project ID: {settings.firebase_project_id}")
        print(f"   Environment: {settings.environment}")
        
        # Try to import Firebase dependencies
        try:
            import firebase_admin
            from firebase_admin import credentials, firestore
            print("✅ Firebase dependencies available")
        except ImportError as e:
            print(f"❌ Missing Firebase dependencies: {e}")
            print("Please install requirements: pip install -r requirements.txt")
            return False
        
        # Initialize Firebase Admin SDK
        if not firebase_admin._apps:
            cred = credentials.Certificate({
                "type": "service_account",
                "project_id": settings.firebase_project_id,
                "private_key_id": settings.firebase_private_key_id,
                "private_key": settings.firebase_private_key,
                "client_email": settings.firebase_client_email,
                "client_id": settings.firebase_client_id,
                "auth_uri": settings.firebase_auth_uri,
                "token_uri": settings.firebase_token_uri,
                "auth_provider_x509_cert_url": settings.firebase_auth_provider_x509_cert_url,
                "client_x509_cert_url": settings.firebase_client_x509_cert_url,
            })
            firebase_admin.initialize_app(cred)
            print("✅ Firebase Admin SDK initialized")
        
        # Test Firestore connection
        db = firestore.client()
        
        # Try to write and read a test document
        test_doc = db.collection("test").document("connection_test")
        test_doc.set({
            "timestamp": firestore.SERVER_TIMESTAMP, 
            "status": "connected",
            "message": "Firebase setup test"
        })
        print("✅ Test document written to Firestore")
        
        # Read it back
        doc = test_doc.get()
        if doc.exists:
            print("✅ Test document read from Firestore")
            
            # Clean up test document
            test_doc.delete()
            print("✅ Test document cleaned up")
            
            print("\n🎉 Firebase connection test successful!")
            return True
        else:
            print("❌ Failed to read test document from Firestore")
            return False
            
    except Exception as e:
        print(f"❌ Firebase connection failed: {e}")
        print("\nTroubleshooting tips:")
        print("1. Check that your Firebase project exists")
        print("2. Verify the service account has proper permissions")
        print("3. Ensure Firestore is enabled in your Firebase project")
        print("4. Check that the private key is properly formatted")
        return False


def print_next_steps():
    """Print next steps for the user."""
    print("\n🎉 Firebase Setup Complete!")
    print("=" * 50)
    print("\n📋 Next Steps:")
    print("1. Install dependencies (if not already done):")
    print("   pip install -r requirements.txt")
    print("   playwright install chromium")
    print("\n2. Start the development server:")
    print("   make dev")
    print("   # or manually: uvicorn app.main:app --reload")
    print("\n3. Test the API:")
    print("   curl http://localhost:8000/api/v1/health/")
    print("   curl http://localhost:8000/api/v1/health/firebase")
    print("\n4. Access API documentation:")
    print("   http://localhost:8000/docs")
    print("\n5. Set up Firestore security rules in Firebase Console")
    print("6. Create your first user account through the API")
    print("\n📚 Documentation:")
    print("- Firebase Setup Guide: docs/FIREBASE_SETUP.md")
    print("- API Reference: docs/API_REFERENCE.md")
    print("- Development Guide: docs/DEVELOPMENT_GUIDE.md")
    print("\n🔒 Security Notes:")
    print("- Never commit your .env file to version control")
    print("- Use different Firebase projects for dev/staging/production")
    print("- Set up proper Firestore security rules before going live")


def main():
    """Main setup function."""
    print_header()
    
    print("This script will help you set up Firebase for Crawl Agent.")
    print("Make sure you have:")
    print("1. ✅ Created a Firebase project")
    print("2. ✅ Enabled Authentication and Firestore Database")
    print("3. ✅ Downloaded the service account JSON file")
    print("4. ✅ Created a web app (for Web API key)")
    print()
    
    if not yes_no_prompt("Ready to continue?"):
        print("Setup cancelled.")
        return False
    
    try:
        # Step 1: Load service account JSON
        print_step(1, "Loading Service Account JSON")
        service_account = load_service_account_json()
        
        # Step 2: Get Web API key
        print_step(2, "Getting Web API Key")
        web_api_key = get_web_api_key()
        
        # Step 3: Create .env file
        if not create_env_file(service_account, web_api_key):
            return False
        
        # Step 4: Test connection
        if not test_firebase_connection():
            print("\n❌ Firebase connection test failed.")
            print("Please check your configuration and try again.")
            print("You can still proceed with development, but Firebase features won't work.")
            if not yes_no_prompt("Continue anyway?", default=False):
                return False
        
        # Print next steps
        print_next_steps()
        
        return True
        
    except KeyboardInterrupt:
        print("\n\n❌ Setup cancelled by user.")
        return False
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n❌ Setup cancelled by user.")
        sys.exit(1)
