"""
Test configuration and fixtures.
"""

import asyncio
import os
import pytest
from typing import AsyncGenerator, Generator
from unittest.mock import Mock, patch

from fastapi.testclient import TestClient
from httpx import AsyncClient

# Set test environment
os.environ["ENVIRONMENT"] = "test"
os.environ["DEBUG"] = "True"
os.environ["SECRET_KEY"] = "test-secret-key"
os.environ["FIREBASE_PROJECT_ID"] = "test-project"
os.environ["FIREBASE_PRIVATE_KEY_ID"] = "test-key-id"
os.environ["FIREBASE_PRIVATE_KEY"] = "-----BEGIN PRIVATE KEY-----\ntest-key\n-----END PRIVATE KEY-----"
os.environ["FIREBASE_CLIENT_EMAIL"] = "<EMAIL>"
os.environ["FIREBASE_CLIENT_ID"] = "test-client-id"
os.environ["FIREBASE_AUTH_URI"] = "https://accounts.google.com/o/oauth2/auth"
os.environ["FIREBASE_TOKEN_URI"] = "https://oauth2.googleapis.com/token"
os.environ["FIREBASE_AUTH_PROVIDER_X509_CERT_URL"] = "https://www.googleapis.com/oauth2/v1/certs"
os.environ["FIREBASE_CLIENT_X509_CERT_URL"] = "https://www.googleapis.com/robot/v1/metadata/x509/test%40test-project.iam.gserviceaccount.com"
os.environ["FIREBASE_WEB_API_KEY"] = "test-web-api-key"

from app.main import app
from app.models.user import User, UserRole


@pytest.fixture(scope="session")
def event_loop() -> Generator[asyncio.AbstractEventLoop, None, None]:
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def client() -> Generator[TestClient, None, None]:
    """Create a test client."""
    with TestClient(app) as test_client:
        yield test_client


@pytest.fixture
async def async_client() -> AsyncGenerator[AsyncClient, None]:
    """Create an async test client."""
    async with AsyncClient(app=app, base_url="http://test") as async_test_client:
        yield async_test_client


@pytest.fixture
def mock_firebase():
    """Mock Firebase configuration."""
    with patch("app.config.firebase.firebase_config") as mock_config:
        # Mock Firebase app
        mock_app = Mock()
        mock_config.app = mock_app
        
        # Mock Firestore client
        mock_firestore = Mock()
        mock_config.firestore = mock_firestore
        
        # Mock Storage bucket
        mock_storage = Mock()
        mock_config.storage = mock_storage
        
        # Mock methods
        mock_config.initialize.return_value = None
        mock_config.health_check.return_value = True
        mock_config.verify_id_token.return_value = {
            "uid": "test-user-id",
            "email": "<EMAIL>",
        }
        mock_config.get_user.return_value = Mock(
            uid="test-user-id",
            email="<EMAIL>",
            display_name="Test User",
            email_verified=True,
            phone_number=None,
            photo_url=None,
            custom_claims={"role": "user"},
            user_metadata=Mock(
                creation_timestamp=**********000,  # 2022-01-01
                last_sign_in_timestamp=**********000,
            ),
        )
        
        yield mock_config


@pytest.fixture
def mock_scraper():
    """Mock web scraping service."""
    with patch("app.services.scraper.WebScrapingService") as mock_service:
        mock_instance = Mock()
        mock_service.return_value.__aenter__.return_value = mock_instance
        mock_service.return_value.__aexit__.return_value = None
        
        # Mock scraping result
        from app.services.scraper import ScrapingResult
        mock_result = ScrapingResult(
            url="https://example.com",
            title="Test Page",
            content="Test content",
            extracted_data={"test": "data"},
            links=["https://example.com/link1"],
            images=["https://example.com/image1.jpg"],
            metadata={"test": "metadata"},
            timestamp=**********.0,
            duration=1.5,
        )
        
        mock_instance.scrape_url.return_value = mock_result
        mock_instance.scrape_multiple_urls.return_value = [mock_result]
        mock_instance.health_check.return_value = True
        
        yield mock_service


@pytest.fixture
def mock_data_processor():
    """Mock data processing service."""
    with patch("app.services.data_processor.DataProcessingService") as mock_service:
        mock_instance = Mock()
        mock_service.return_value = mock_instance
        
        # Mock processed data
        mock_instance.clean_data.return_value = {"content": "cleaned content"}
        mock_instance.transform_data.return_value = Mock(
            data={"transformed": "data"},
            metadata={"total_items": 1},
            processing_info={"cleaning_applied": True},
            timestamp="2022-01-01T00:00:00Z",
        )
        
        yield mock_service


@pytest.fixture
def test_user() -> User:
    """Create a test user."""
    from datetime import datetime
    
    return User(
        uid="test-user-id",
        email="<EMAIL>",
        display_name="Test User",
        role=UserRole.USER,
        status="active",
        created_at=datetime(2022, 1, 1),
        updated_at=datetime(2022, 1, 1),
        email_verified=True,
        custom_claims={"role": "user"},
    )


@pytest.fixture
def admin_user() -> User:
    """Create a test admin user."""
    from datetime import datetime
    
    return User(
        uid="admin-user-id",
        email="<EMAIL>",
        display_name="Admin User",
        role=UserRole.ADMIN,
        status="active",
        created_at=datetime(2022, 1, 1),
        updated_at=datetime(2022, 1, 1),
        email_verified=True,
        custom_claims={"role": "admin"},
    )


@pytest.fixture
def auth_headers(test_user: User) -> dict:
    """Create authentication headers for test user."""
    from app.core.security import create_access_token
    
    token = create_access_token(data={"sub": test_user.uid, "email": test_user.email})
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
def admin_auth_headers(admin_user: User) -> dict:
    """Create authentication headers for admin user."""
    from app.core.security import create_access_token
    
    token = create_access_token(data={"sub": admin_user.uid, "email": admin_user.email})
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
def sample_scraping_config():
    """Sample scraping configuration."""
    from app.services.scraper import ScrapingConfig
    
    return ScrapingConfig(
        url="https://example.com",
        max_depth=1,
        max_pages=10,
        delay=1.0,
        timeout=30,
        javascript=True,
        screenshot=False,
        pdf=False,
    )


@pytest.fixture
def sample_extraction_config():
    """Sample extraction configuration."""
    from app.services.scraper import ExtractionConfig
    
    return ExtractionConfig(
        strategy="css",
        selectors={"title": "h1", "content": "p"},
        similarity_threshold=0.8,
    )


@pytest.fixture
def sample_scraping_job():
    """Sample scraping job."""
    from datetime import datetime
    from app.models.scraping_job import ScrapingJob, ScrapingJobConfig, JobStatus, JobPriority
    
    config = ScrapingJobConfig(
        urls=["https://example.com"],
        max_depth=1,
        max_pages=10,
        delay=1.0,
        timeout=30,
        javascript=True,
        extraction_strategy="css",
        css_selectors={"title": "h1"},
        clean_data=True,
        output_format="json",
    )
    
    return ScrapingJob(
        job_id="test-job-id",
        user_id="test-user-id",
        name="Test Job",
        description="Test scraping job",
        config=config,
        status=JobStatus.PENDING,
        priority=JobPriority.NORMAL,
        tags=["test"],
        created_at=datetime(2022, 1, 1),
        updated_at=datetime(2022, 1, 1),
    )


@pytest.fixture(autouse=True)
def setup_test_environment():
    """Set up test environment."""
    # Mock external dependencies
    with patch("app.config.firebase.firebase_admin.initialize_app"):
        with patch("app.config.firebase.firestore.client"):
            with patch("app.config.firebase.storage.bucket"):
                yield


class MockFirestoreDocument:
    """Mock Firestore document."""
    
    def __init__(self, data: dict, exists: bool = True):
        self._data = data
        self._exists = exists
    
    def exists(self) -> bool:
        return self._exists
    
    def to_dict(self) -> dict:
        return self._data
    
    def get(self):
        return self
    
    def set(self, data: dict):
        self._data = data
    
    def update(self, updates: dict):
        self._data.update(updates)
    
    def delete(self):
        self._exists = False


class MockFirestoreCollection:
    """Mock Firestore collection."""
    
    def __init__(self):
        self._documents = {}
    
    def document(self, doc_id: str):
        if doc_id not in self._documents:
            self._documents[doc_id] = MockFirestoreDocument({}, exists=False)
        return self._documents[doc_id]
    
    def where(self, field: str, op: str, value):
        return MockFirestoreQuery(self._documents)
    
    def order_by(self, field: str, direction=None):
        return MockFirestoreQuery(self._documents)
    
    def limit(self, count: int):
        return MockFirestoreQuery(self._documents)
    
    def offset(self, count: int):
        return MockFirestoreQuery(self._documents)


class MockFirestoreQuery:
    """Mock Firestore query."""
    
    def __init__(self, documents: dict):
        self._documents = documents
    
    def where(self, field: str, op: str, value):
        return self
    
    def order_by(self, field: str, direction=None):
        return self
    
    def limit(self, count: int):
        return self
    
    def offset(self, count: int):
        return self
    
    def stream(self):
        return [doc for doc in self._documents.values() if doc.exists()]
    
    def get(self):
        return [doc for doc in self._documents.values() if doc.exists()]


@pytest.fixture
def mock_firestore():
    """Mock Firestore client."""
    mock_client = Mock()
    mock_client.collection.return_value = MockFirestoreCollection()
    return mock_client
