"""
Tests for web scraping service.
"""

import pytest
from unittest.mock import As<PERSON><PERSON><PERSON>, Mock, patch

from app.services.scraper import (
    ExtractionConfig,
    ScrapingConfig,
    ScrapingResult,
    WebScrapingService,
)


class TestScrapingConfig:
    """Test scraping configuration."""
    
    def test_valid_config(self):
        """Test valid scraping configuration."""
        config = ScrapingConfig(
            url="https://example.com",
            max_depth=2,
            max_pages=50,
            delay=2.0,
            timeout=60,
            javascript=True,
        )
        
        assert config.url == "https://example.com"
        assert config.max_depth == 2
        assert config.max_pages == 50
        assert config.delay == 2.0
        assert config.timeout == 60
        assert config.javascript is True
    
    def test_invalid_url(self):
        """Test invalid URL validation."""
        with pytest.raises(ValueError, match="Invalid URL format"):
            ScrapingConfig(url="not-a-url")
    
    def test_invalid_proxy(self):
        """Test invalid proxy URL validation."""
        with pytest.raises(ValueError, match="Invalid proxy URL format"):
            ScrapingConfig(
                url="https://example.com",
                proxy="not-a-proxy-url"
            )
    
    def test_default_values(self):
        """Test default configuration values."""
        config = ScrapingConfig(url="https://example.com")
        
        assert config.max_depth == 1
        assert config.max_pages == 10
        assert config.delay == 1.0
        assert config.timeout == 30
        assert config.javascript is True
        assert config.screenshot is False
        assert config.pdf is False


class TestExtractionConfig:
    """Test extraction configuration."""
    
    def test_valid_css_strategy(self):
        """Test valid CSS extraction strategy."""
        config = ExtractionConfig(
            strategy="css",
            selectors={"title": "h1", "content": "p"},
        )
        
        assert config.strategy == "css"
        assert config.selectors == {"title": "h1", "content": "p"}
    
    def test_valid_llm_strategy(self):
        """Test valid LLM extraction strategy."""
        config = ExtractionConfig(
            strategy="llm",
            llm_prompt="Extract the main content from this page",
        )
        
        assert config.strategy == "llm"
        assert config.llm_prompt == "Extract the main content from this page"
    
    def test_invalid_strategy(self):
        """Test invalid extraction strategy."""
        with pytest.raises(ValueError, match="Strategy must be one of"):
            ExtractionConfig(strategy="invalid")
    
    def test_default_values(self):
        """Test default extraction configuration values."""
        config = ExtractionConfig()
        
        assert config.strategy == "css"
        assert config.similarity_threshold == 0.8


class TestWebScrapingService:
    """Test web scraping service."""
    
    @pytest.fixture
    def mock_crawler(self):
        """Mock AsyncWebCrawler."""
        with patch("app.services.scraper.AsyncWebCrawler") as mock:
            crawler_instance = AsyncMock()
            mock.return_value = crawler_instance
            
            # Mock successful crawl result
            mock_result = Mock()
            mock_result.success = True
            mock_result.cleaned_html = "Test content"
            mock_result.extracted_content = {"test": "data"}
            mock_result.links = {"internal": ["link1"], "external": ["link2"]}
            mock_result.media = {"images": ["image1.jpg"]}
            mock_result.metadata = {"title": "Test Page"}
            mock_result.screenshot = None
            mock_result.pdf = None
            mock_result.error_message = None
            
            crawler_instance.arun.return_value = mock_result
            
            yield mock, crawler_instance
    
    @pytest.mark.asyncio
    async def test_scrape_url_success(self, mock_crawler, sample_scraping_config):
        """Test successful URL scraping."""
        mock_class, mock_instance = mock_crawler
        
        service = WebScrapingService()
        await service.start_session()
        
        result = await service.scrape_url(sample_scraping_config)
        
        assert isinstance(result, ScrapingResult)
        assert result.url == sample_scraping_config.url
        assert result.title == "Test Page"
        assert result.content == "Test content"
        assert result.extracted_data == {"test": "data"}
        assert result.links == ["link1", "link2"]
        assert result.images == ["image1.jpg"]
        assert result.error is None
        
        # Verify crawler was called with correct parameters
        mock_instance.arun.assert_called_once()
        call_args = mock_instance.arun.call_args[1]
        assert call_args["url"] == sample_scraping_config.url
        assert call_args["bypass_cache"] is True
        assert call_args["include_raw_html"] is True
    
    @pytest.mark.asyncio
    async def test_scrape_url_failure(self, mock_crawler, sample_scraping_config):
        """Test URL scraping failure."""
        mock_class, mock_instance = mock_crawler
        
        # Mock failed crawl result
        mock_result = Mock()
        mock_result.success = False
        mock_result.error_message = "Failed to load page"
        mock_instance.arun.return_value = mock_result
        
        service = WebScrapingService()
        await service.start_session()
        
        result = await service.scrape_url(sample_scraping_config)
        
        assert isinstance(result, ScrapingResult)
        assert result.url == sample_scraping_config.url
        assert result.error == "Failed to scrape URL: Failed to load page"
        assert result.content is None
    
    @pytest.mark.asyncio
    async def test_scrape_url_with_extraction(self, mock_crawler, sample_scraping_config, sample_extraction_config):
        """Test URL scraping with extraction configuration."""
        mock_class, mock_instance = mock_crawler
        
        service = WebScrapingService()
        await service.start_session()
        
        result = await service.scrape_url(sample_scraping_config, sample_extraction_config)
        
        assert isinstance(result, ScrapingResult)
        assert result.error is None
        
        # Verify extraction strategy was set
        mock_instance.arun.assert_called_once()
        call_args = mock_instance.arun.call_args[1]
        assert "extraction_strategy" in call_args
    
    @pytest.mark.asyncio
    async def test_scrape_multiple_urls(self, mock_crawler, sample_scraping_config):
        """Test scraping multiple URLs."""
        mock_class, mock_instance = mock_crawler
        
        urls = ["https://example1.com", "https://example2.com", "https://example3.com"]
        
        service = WebScrapingService()
        await service.start_session()
        
        results = await service.scrape_multiple_urls(
            urls=urls,
            config=sample_scraping_config,
            max_concurrent=2,
        )
        
        assert len(results) == 3
        for i, result in enumerate(results):
            assert isinstance(result, ScrapingResult)
            assert result.url == urls[i]
        
        # Verify crawler was called for each URL
        assert mock_instance.arun.call_count == 3
    
    @pytest.mark.asyncio
    async def test_scrape_multiple_urls_with_errors(self, mock_crawler, sample_scraping_config):
        """Test scraping multiple URLs with some errors."""
        mock_class, mock_instance = mock_crawler
        
        # Mock mixed results (success and failure)
        def side_effect(*args, **kwargs):
            url = kwargs.get("url", "")
            if "error" in url:
                raise Exception("Scraping error")
            
            mock_result = Mock()
            mock_result.success = True
            mock_result.cleaned_html = "Test content"
            mock_result.extracted_content = {"test": "data"}
            mock_result.links = {"internal": [], "external": []}
            mock_result.media = {"images": []}
            mock_result.metadata = {"title": "Test Page"}
            mock_result.screenshot = None
            mock_result.pdf = None
            return mock_result
        
        mock_instance.arun.side_effect = side_effect
        
        urls = ["https://example.com", "https://error.com", "https://example2.com"]
        
        service = WebScrapingService()
        await service.start_session()
        
        results = await service.scrape_multiple_urls(
            urls=urls,
            config=sample_scraping_config,
        )
        
        assert len(results) == 3
        assert results[0].error is None  # Success
        assert results[1].error is not None  # Error
        assert results[2].error is None  # Success
    
    @pytest.mark.asyncio
    async def test_context_manager(self, mock_crawler):
        """Test using service as context manager."""
        mock_class, mock_instance = mock_crawler
        
        async with WebScrapingService() as service:
            assert service.session_active is True
        
        # Verify session was started and closed
        mock_instance.astart.assert_called_once()
        mock_instance.aclose.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_health_check_success(self, mock_crawler):
        """Test health check success."""
        mock_class, mock_instance = mock_crawler
        
        # Mock successful health check
        mock_result = Mock()
        mock_result.success = True
        mock_result.error = None
        mock_instance.arun.return_value = mock_result
        
        service = WebScrapingService()
        is_healthy = await service.health_check()
        
        assert is_healthy is True
    
    @pytest.mark.asyncio
    async def test_health_check_failure(self, mock_crawler):
        """Test health check failure."""
        mock_class, mock_instance = mock_crawler
        
        # Mock failed health check
        mock_result = Mock()
        mock_result.error = "Health check failed"
        mock_instance.arun.return_value = mock_result
        
        service = WebScrapingService()
        is_healthy = await service.health_check()
        
        assert is_healthy is False
    
    @pytest.mark.asyncio
    async def test_extraction_strategy_css(self):
        """Test CSS extraction strategy creation."""
        config = ExtractionConfig(
            strategy="css",
            selectors={"title": "h1", "content": "p"},
        )
        
        service = WebScrapingService()
        strategy = service._get_extraction_strategy(config)
        
        assert strategy is not None
    
    @pytest.mark.asyncio
    async def test_extraction_strategy_llm(self):
        """Test LLM extraction strategy creation."""
        with patch("app.services.scraper.LLMExtractionStrategy") as mock_strategy:
            config = ExtractionConfig(
                strategy="llm",
                llm_prompt="Extract content",
            )
            
            service = WebScrapingService()
            strategy = service._get_extraction_strategy(config)
            
            mock_strategy.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_extraction_strategy_cosine(self):
        """Test Cosine extraction strategy creation."""
        with patch("app.services.scraper.CosineStrategy") as mock_strategy:
            config = ExtractionConfig(
                strategy="cosine",
                similarity_threshold=0.9,
            )
            
            service = WebScrapingService()
            strategy = service._get_extraction_strategy(config)
            
            mock_strategy.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_extraction_strategy_none(self):
        """Test extraction strategy returns None for invalid config."""
        config = ExtractionConfig(strategy="css")  # No selectors
        
        service = WebScrapingService()
        strategy = service._get_extraction_strategy(config)
        
        assert strategy is None
