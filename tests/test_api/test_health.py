"""
Tests for health check endpoints.
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch


class TestHealthEndpoints:
    """Test health check endpoints."""
    
    def test_health_check_success(self, client: TestClient, mock_firebase, mock_scraper):
        """Test successful health check."""
        response = client.get("/api/v1/health/")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] in ["healthy", "degraded"]
        assert "timestamp" in data
        assert "version" in data
        assert "environment" in data
        assert "uptime_seconds" in data
        assert "checks" in data
        
        # Check individual service checks
        checks = data["checks"]
        assert "firebase" in checks
        assert "scraper" in checks
        assert "system" in checks
        
        # Verify check structure
        for service, check in checks.items():
            assert "status" in check
            assert "response_time_ms" in check
            assert "message" in check
            assert check["status"] in ["healthy", "unhealthy", "degraded", "unknown"]
    
    def test_liveness_probe(self, client: TestClient):
        """Test liveness probe endpoint."""
        response = client.get("/api/v1/health/live")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "alive"
        assert "timestamp" in data
    
    def test_readiness_probe_success(self, client: TestClient, mock_firebase):
        """Test successful readiness probe."""
        mock_firebase.health_check.return_value = True
        
        response = client.get("/api/v1/health/ready")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "ready"
        assert "timestamp" in data
        assert "checks" in data
    
    def test_readiness_probe_failure(self, client: TestClient, mock_firebase):
        """Test readiness probe failure."""
        mock_firebase.health_check.return_value = False
        
        response = client.get("/api/v1/health/ready")
        
        assert response.status_code == 503
        data = response.json()
        
        assert "detail" in data
        assert "Firebase connection failed" in data["detail"]
    
    def test_firebase_health_success(self, client: TestClient, mock_firebase):
        """Test Firebase health check success."""
        mock_firebase.health_check.return_value = True
        
        response = client.get("/api/v1/health/firebase")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "healthy"
        assert data["message"] == "Firebase connection successful"
        assert "response_time_ms" in data
        assert "details" in data
    
    def test_firebase_health_failure(self, client: TestClient, mock_firebase):
        """Test Firebase health check failure."""
        mock_firebase.health_check.return_value = False
        
        response = client.get("/api/v1/health/firebase")
        
        assert response.status_code == 503
        data = response.json()
        
        assert "detail" in data
        assert "Firebase unhealthy" in data["detail"]
    
    def test_scraper_health_success(self, client: TestClient, mock_scraper):
        """Test scraper health check success."""
        response = client.get("/api/v1/health/scraper")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "healthy"
        assert data["message"] == "Scraper service operational"
        assert "response_time_ms" in data
        assert "details" in data
    
    def test_scraper_health_failure(self, client: TestClient, mock_scraper):
        """Test scraper health check failure."""
        # Mock scraper health check failure
        mock_instance = mock_scraper.return_value.__aenter__.return_value
        mock_instance.health_check.return_value = False
        
        response = client.get("/api/v1/health/scraper")
        
        assert response.status_code == 503
        data = response.json()
        
        assert "detail" in data
        assert "Scraper service unhealthy" in data["detail"]
    
    def test_health_check_with_firebase_error(self, client: TestClient, mock_firebase, mock_scraper):
        """Test health check when Firebase has errors."""
        mock_firebase.health_check.side_effect = Exception("Firebase connection error")
        
        response = client.get("/api/v1/health/")
        
        assert response.status_code == 200
        data = response.json()
        
        # Should still return 200 but with unhealthy status
        assert data["status"] == "unhealthy"
        assert data["checks"]["firebase"]["status"] == "unhealthy"
        assert "Firebase connection error" in data["checks"]["firebase"]["message"]
    
    def test_health_check_with_scraper_error(self, client: TestClient, mock_firebase, mock_scraper):
        """Test health check when scraper has errors."""
        mock_instance = mock_scraper.return_value.__aenter__.return_value
        mock_instance.health_check.side_effect = Exception("Scraper error")
        
        response = client.get("/api/v1/health/")
        
        assert response.status_code == 200
        data = response.json()
        
        # Should be degraded if only scraper fails
        assert data["status"] in ["degraded", "unhealthy"]
        assert data["checks"]["scraper"]["status"] == "unhealthy"
        assert "Scraper error" in data["checks"]["scraper"]["message"]
    
    @pytest.mark.skipif(
        not pytest.importorskip("psutil", reason="psutil not available"),
        reason="psutil not available"
    )
    def test_system_resource_check(self, client: TestClient, mock_firebase, mock_scraper):
        """Test system resource check."""
        with patch("app.api.v1.health.psutil") as mock_psutil:
            # Mock system metrics
            mock_psutil.cpu_percent.return_value = 50.0
            mock_psutil.virtual_memory.return_value.percent = 60.0
            mock_psutil.virtual_memory.return_value.available = 4 * 1024**3  # 4GB
            mock_psutil.disk_usage.return_value.percent = 70.0
            mock_psutil.disk_usage.return_value.free = 100 * 1024**3  # 100GB
            
            response = client.get("/api/v1/health/")
            
            assert response.status_code == 200
            data = response.json()
            
            system_check = data["checks"]["system"]
            assert system_check["status"] == "healthy"
            assert "cpu_percent" in system_check["details"]
            assert "memory_percent" in system_check["details"]
            assert "disk_percent" in system_check["details"]
    
    def test_system_resource_check_high_usage(self, client: TestClient, mock_firebase, mock_scraper):
        """Test system resource check with high usage."""
        with patch("app.api.v1.health.psutil") as mock_psutil:
            # Mock high system usage
            mock_psutil.cpu_percent.return_value = 95.0
            mock_psutil.virtual_memory.return_value.percent = 95.0
            mock_psutil.virtual_memory.return_value.available = 1 * 1024**3  # 1GB
            mock_psutil.disk_usage.return_value.percent = 95.0
            mock_psutil.disk_usage.return_value.free = 5 * 1024**3  # 5GB
            
            response = client.get("/api/v1/health/")
            
            assert response.status_code == 200
            data = response.json()
            
            system_check = data["checks"]["system"]
            assert system_check["status"] == "unhealthy"
            assert "High CPU usage" in system_check["message"]
            assert "High memory usage" in system_check["message"]
            assert "High disk usage" in system_check["message"]
    
    def test_system_resource_check_without_psutil(self, client: TestClient, mock_firebase, mock_scraper):
        """Test system resource check when psutil is not available."""
        with patch("app.api.v1.health.psutil", side_effect=ImportError("psutil not available")):
            response = client.get("/api/v1/health/")
            
            assert response.status_code == 200
            data = response.json()
            
            system_check = data["checks"]["system"]
            assert system_check["status"] == "unknown"
            assert "System monitoring not available" in system_check["message"]
