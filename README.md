# Crawl Agent - Web Scraping & Data Extraction Platform

A production-ready FastAPI application with Firebase integration for dynamic web scraping and data extraction using crawl4ai.

## Features

- 🚀 **FastAPI** - Modern, fast web framework for building APIs
- 🔥 **Firebase Integration** - Authentication, Firestore database, and storage
- 🕷️ **Crawl4AI** - Advanced web scraping with AI-powered extraction
- 🐳 **Docker Support** - Containerized deployment
- 📊 **Data Processing** - Advanced data transformation and validation
- 🔐 **Authentication** - Firebase Auth with role-based access control
- 📈 **Monitoring** - Comprehensive logging and health checks
- ⚡ **Async Processing** - Background task processing for large jobs
- 🧪 **Testing** - Comprehensive test suite
- 🔄 **CI/CD Ready** - Production deployment configurations

## Project Structure

```
crawl-agent/
├── app/
│   ├── __init__.py
│   ├── main.py                 # FastAPI application entry point
│   ├── config/
│   │   ├── __init__.py
│   │   ├── settings.py         # Application settings
│   │   └── firebase.py         # Firebase configuration
│   ├── api/
│   │   ├── __init__.py
│   │   ├── deps.py             # API dependencies
│   │   └── v1/
│   │       ├── __init__.py
│   │       ├── auth.py         # Authentication endpoints
│   │       ├── scraping.py     # Scraping endpoints
│   │       ├── data.py         # Data management endpoints
│   │       └── health.py       # Health check endpoints
│   ├── core/
│   │   ├── __init__.py
│   │   ├── security.py         # Security utilities
│   │   ├── exceptions.py       # Custom exceptions
│   │   └── middleware.py       # Custom middleware
│   ├── services/
│   │   ├── __init__.py
│   │   ├── scraper.py          # Web scraping service
│   │   ├── data_processor.py   # Data processing service
│   │   ├── firebase_service.py # Firebase operations
│   │   └── task_queue.py       # Background task processing
│   ├── models/
│   │   ├── __init__.py
│   │   ├── user.py             # User models
│   │   ├── scraping_job.py     # Scraping job models
│   │   └── data_models.py      # Data structure models
│   └── utils/
│       ├── __init__.py
│       ├── logger.py           # Logging configuration
│       └── validators.py       # Data validation utilities
├── tests/
│   ├── __init__.py
│   ├── conftest.py             # Test configuration
│   ├── test_api/
│   ├── test_services/
│   └── test_utils/
├── scripts/
│   ├── setup_firebase.py       # Firebase setup script
│   └── migrate_data.py         # Data migration script
├── docker/
│   ├── Dockerfile
│   ├── docker-compose.yml
│   └── docker-compose.prod.yml
├── .env.example                # Environment variables template
├── .gitignore
├── requirements.txt            # Python dependencies
├── pyproject.toml             # Project configuration
└── README.md
```

## Quick Start

1. **Clone and Setup**
   ```bash
   git clone <repository-url>
   cd crawl-agent
   cp .env.example .env
   # Edit .env with your configuration
   ```

2. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Setup Firebase**
   ```bash
   python scripts/setup_firebase.py
   ```

4. **Run Development Server**
   ```bash
   uvicorn app.main:app --reload
   ```

5. **Access API Documentation**
   - Swagger UI: http://localhost:8000/docs
   - ReDoc: http://localhost:8000/redoc

## Docker Deployment

```bash
# Development
docker-compose up -d

# Production
docker-compose -f docker/docker-compose.prod.yml up -d
```

## API Endpoints

- `POST /api/v1/scrape` - Start scraping job
- `GET /api/v1/jobs/{job_id}` - Get job status
- `GET /api/v1/data/{job_id}` - Get scraped data
- `POST /api/v1/auth/login` - User authentication
- `GET /api/v1/health` - Health check

## Environment Variables

See `.env.example` for required environment variables.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License
